"""
测试继续训练功能
"""

import json
import os
from patent_stock_predictor import (
    patent_train_model, 
    patent_continue_training, 
    patent_train_with_new_data,
    generate_sample_data,
    get_recommended_early_stopping_params
)

def test_continue_training():
    """测试继续训练功能"""
    print("=" * 80)
    print("🧪 测试继续训练功能")
    print("=" * 80)
    
    # 生成示例数据
    print("生成示例数据...")
    num_stocks = 2
    days_per_stock = 30
    sample_data = generate_sample_data(num_stocks=num_stocks, days_per_stock=days_per_stock, k=5)
    print(f"生成了{len(sample_data)}只股票的数据，每只股票{len(sample_data[0])}天")
    
    # 获取推荐参数
    recommended_params = get_recommended_early_stopping_params(num_stocks, days_per_stock)
    print(f"推荐参数: {recommended_params}")
    
    try:
        # 第一阶段：初始训练
        print("\n🚀 第一阶段：初始训练")
        model1, scaler1 = patent_train_model(
            kline_data_list=sample_data,
            k=5,
            epochs_pretrain=3,  # 较少轮数用于测试
            epochs_finetune=2,
            model_path='test_initial_model.pth',
            early_stopping=False  # 关闭早停以确保完整训练
        )
        print("✅ 初始训练完成")
        
        # 第二阶段：继续训练（继承轮数）
        print("\n🔄 第二阶段：继续训练（继承轮数）")
        model2, scaler2 = patent_continue_training(
            kline_data_list=sample_data,
            pretrained_model_path='test_initial_model.pth',
            additional_epochs_pretrain=2,
            additional_epochs_finetune=1,
            inherit_epochs=True,
            model_path='test_continued_model.pth',
            early_stopping=False
        )
        print("✅ 继续训练完成")
        
        # 第三阶段：新数据训练（不继承轮数）
        print("\n🆕 第三阶段：新数据训练（不继承轮数）")
        model3, scaler3 = patent_train_with_new_data(
            kline_data_list=sample_data,  # 这里可以是不同的数据
            pretrained_model_path='test_initial_model.pth',
            epochs_pretrain=3,
            epochs_finetune=2,
            model_path='test_new_data_model.pth',
            early_stopping=False
        )
        print("✅ 新数据训练完成")
        
        # 验证模型文件
        models_to_check = [
            'test_initial_model.pth',
            'test_continued_model.pth', 
            'test_new_data_model.pth'
        ]
        
        print("\n📊 验证保存的模型信息:")
        for model_path in models_to_check:
            if os.path.exists(model_path):
                import torch
                checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
                if 'epochs_info' in checkpoint:
                    epochs_info = checkpoint['epochs_info']
                    print(f"  {model_path}: 预训练{epochs_info['pretrain']}轮, 微调{epochs_info['finetune']}轮")
                else:
                    print(f"  {model_path}: 没有轮数信息")
            else:
                print(f"  {model_path}: 文件不存在")
        
        print("\n🎉 所有测试完成！")
        
        # 清理测试文件
        print("\n🧹 清理测试文件...")
        for model_path in models_to_check:
            if os.path.exists(model_path):
                os.remove(model_path)
                print(f"  删除: {model_path}")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_continue_training()
