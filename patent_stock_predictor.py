"""
基于多任务自监督学习的股票涨跌预测方法
严格按照专利CN113159945A实现

专利步骤：
步骤1：数据收集和预处理（6维：最高价、最低价、开盘价、收盘价、成交量、成交金额）
步骤2：锚-样本序列对采样（正采样+时序负采样+对比负采样）
步骤3：正负样本判别自监督辅助任务
步骤4：价格变化同向性自监督辅助任务
步骤5：成交量变化同向性自监督辅助任务
步骤6：三种任务联合训练
步骤7：提取序列编码器，编码得到表征因子
步骤8：基于LSTM的股票涨跌预测
步骤9：测试预测

核心功能：
1. 完整两阶段训练：patent_train_model(kline_data_list, k=5, epochs_pretrain=50, epochs_finetune=20)
2. 一键预测：patent_predict(kline_data, model_path='patent_model.pth')
3. 早停机制：自动防止过拟合，提高训练效率

早停机制使用说明：
- early_stopping=True: 启用早停机制
- patience=10: 连续10轮无改善后停止训练
- min_delta=1e-4: 损失改善的最小阈值
- 推荐使用 get_recommended_early_stopping_params() 获取合适参数
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import math
import os
import warnings
import json
warnings.filterwarnings('ignore')

# ============================================================================
# 专利要求的模型组件 - 严格按照CN113159945A实现
# ============================================================================

class PatentTransformerLayer(nn.Module):
    """专利要求的Transformer层：多头注意力 + 前馈网络"""
    def __init__(self, d_model, n_heads, d_ff, dropout=0.1):
        super(PatentTransformerLayer, self).__init__()
        
        # 多头注意力机制模块
        self.multi_head_attention = nn.MultiheadAttention(
            d_model, n_heads, dropout=dropout, batch_first=True
        )
        
        # 前馈神经网络模块
        self.ffn = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.ReLU(),
            nn.Linear(d_ff, d_model)
        )
        
        # 残差连接和层归一化
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x):
        # 多头注意力 + 残差连接
        attn_output, _ = self.multi_head_attention(x, x, x)
        x = self.norm1(x + self.dropout(attn_output))
        
        # 前馈网络 + 残差连接
        ffn_output = self.ffn(x)
        x = self.norm2(x + self.dropout(ffn_output))
        
        return x

class PatentAttentionPooling(nn.Module):
    """专利要求的注意力机制层：获取序列总表征"""
    def __init__(self, d_model):
        super(PatentAttentionPooling, self).__init__()
        self.attention = nn.MultiheadAttention(d_model, num_heads=4, batch_first=True)
        # 可学习的查询向量
        self.query = nn.Parameter(torch.randn(1, 1, d_model))
    
    def forward(self, x):
        # x: (batch_size, seq_len, d_model)
        batch_size = x.size(0)
        query = self.query.expand(batch_size, -1, -1)  # (batch_size, 1, d_model)
        
        # 注意力池化
        output, _ = self.attention(query, x, x)  # (batch_size, 1, d_model)
        return output.squeeze(1)  # (batch_size, d_model)

class PatentSequenceEncoder(nn.Module):
    """专利要求的序列编码器：Transformer层 + 注意力机制层"""
    def __init__(self, input_dim=6, d_model=128, n_heads=4, n_layers=3, d_ff=256, dropout=0.1):
        super(PatentSequenceEncoder, self).__init__()
        
        # 输入投影
        self.input_projection = nn.Linear(input_dim, d_model)
        
        # Transformer层
        self.transformer_layers = nn.ModuleList([
            PatentTransformerLayer(d_model, n_heads, d_ff, dropout)
            for _ in range(n_layers)
        ])
        
        # 注意力机制层
        self.attention_pooling = PatentAttentionPooling(d_model)
    
    def forward(self, x):
        # 输入投影
        x = self.input_projection(x)  # (batch_size, seq_len, d_model)
        
        # Transformer层
        for layer in self.transformer_layers:
            x = layer(x)
        
        # 注意力机制层：获取序列总表征
        sequence_repr = self.attention_pooling(x)  # (batch_size, d_model)
        
        return sequence_repr

class PatentSelfSupervisedTasks(nn.Module):
    """专利要求的三个自监督辅助任务"""
    def __init__(self, d_model):
        super(PatentSelfSupervisedTasks, self).__init__()
        
        # 任务1：正负样本判别任务
        self.positive_negative_classifier = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model, 2)
        )
        
        # 任务2：价格变化同向性任务
        self.price_direction_classifier = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model, 2)
        )
        
        # 任务3：成交量变化同向性任务
        self.volume_direction_classifier = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model, 2)
        )
    
    def forward(self, anchor_repr, sample_repr, task_labels):
        # 拼接锚序列和样本序列的表征
        combined_repr = torch.cat([anchor_repr, sample_repr], dim=-1)
        
        losses = {}
        
        # 任务1：正负样本判别
        if 'positive_negative' in task_labels:
            pos_neg_logits = self.positive_negative_classifier(combined_repr)
            pos_neg_loss = F.cross_entropy(pos_neg_logits, task_labels['positive_negative'])
            losses['positive_negative_loss'] = pos_neg_loss
        
        # 任务2：价格变化同向性
        if 'price_direction' in task_labels:
            price_dir_logits = self.price_direction_classifier(combined_repr)
            price_dir_loss = F.cross_entropy(price_dir_logits, task_labels['price_direction'])
            losses['price_direction_loss'] = price_dir_loss
        
        # 任务3：成交量变化同向性
        if 'volume_direction' in task_labels:
            volume_dir_logits = self.volume_direction_classifier(combined_repr)
            volume_dir_loss = F.cross_entropy(volume_dir_logits, task_labels['volume_direction'])
            losses['volume_direction_loss'] = volume_dir_loss
        
        return losses

class PatentLSTMPredictor(nn.Module):
    """专利要求的LSTM预测器"""
    def __init__(self, d_model, hidden_size=64, num_layers=2, dropout=0.1):
        super(PatentLSTMPredictor, self).__init__()
        
        # LSTM网络
        self.lstm = nn.LSTM(
            input_size=d_model,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )
        
        # 前馈神经网络进行二分类
        self.classifier = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, 2)  # 二分类：涨或跌
        )
    
    def forward(self, sequence_embeddings):
        # sequence_embeddings: (batch_size, seq_len, d_model)
        lstm_out, _ = self.lstm(sequence_embeddings)
        
        # 取最后一个时间步的隐层表征
        last_hidden = lstm_out[:, -1, :]  # (batch_size, hidden_size)
        
        # 二分类预测
        prediction = self.classifier(last_hidden)
        return prediction

class PatentStockPredictor(nn.Module):
    """专利完整模型：序列编码器 + 自监督任务 + LSTM预测器"""
    def __init__(self, input_dim=6, d_model=128, n_heads=4, n_layers=3):
        super(PatentStockPredictor, self).__init__()
        
        self.encoder = PatentSequenceEncoder(input_dim, d_model, n_heads, n_layers)
        self.ssl_tasks = PatentSelfSupervisedTasks(d_model)
        self.lstm_predictor = PatentLSTMPredictor(d_model)
    
    def forward(self, anchor_seq, sample_seq=None, task_labels=None, mode='pretrain'):
        if mode == 'pretrain' and sample_seq is not None:
            # 自监督预训练模式
            anchor_repr = self.encoder(anchor_seq)
            sample_repr = self.encoder(sample_seq)
            ssl_losses = self.ssl_tasks(anchor_repr, sample_repr, task_labels)
            return ssl_losses
        
        elif mode == 'extract_features':
            # 特征提取模式
            with torch.no_grad():
                sequence_repr = self.encoder(anchor_seq)
            return sequence_repr
        
        elif mode == 'predict':
            # 预测模式
            prediction = self.lstm_predictor(anchor_seq)
            return prediction
        
        else:
            # 默认编码模式
            sequence_repr = self.encoder(anchor_seq)
            return sequence_repr

# ============================================================================
# 专利要求的数据处理组件
# ============================================================================

def extract_patent_features(kline_data):
    """
    按专利要求提取6维特征
    
    Args:
        kline_data: 单条K线数据 [开盘时间, 开盘价, 最高价, 最低价, 收盘价, 成交量, 收盘时间, 成交额, ...]
    
    Returns:
        features: [最高价, 最低价, 开盘价, 收盘价, 成交量, 成交金额] (专利要求的6维)
    """
    high_price = float(kline_data[2])      # 最高价
    low_price = float(kline_data[3])       # 最低价
    open_price = float(kline_data[1])      # 开盘价
    close_price = float(kline_data[4])     # 收盘价
    volume = float(kline_data[5])          # 成交量
    turnover = float(kline_data[7])        # 成交金额
    
    return [high_price, low_price, open_price, close_price, volume, turnover]

def create_patent_anchor_sample_pairs(stock_data_list, k=5, m=10):
    """
    按专利要求创建锚-样本序列对
    严格按照专利CN113159945A的采样方式和标签计算方法

    Args:
        stock_data_list: 多只股票的数据列表，每只股票是一个时间序列
        k: 序列长度
        m: 时序负采样的距离天数

    Returns:
        anchor_sample_pairs: 锚-样本序列对列表
    """
    pairs = []

    for stock_idx, stock_data in enumerate(stock_data_list):
        if len(stock_data) < k + m + k + 2:  # 确保有足够的数据（包括下一个交易日）
            continue

        # 遍历可能的锚序列位置
        for t in range(k-1, len(stock_data) - k - 2):  # t是锚序列的结束时间，确保有下一个交易日数据

            # 锚序列：X1 = S1[t-k+1:t+1] (专利公式)
            anchor_seq = stock_data[t-k+1:t+1]
            anchor_next_day = stock_data[t+1] if t+1 < len(stock_data) else None

            if anchor_next_day is None:
                continue

            # 正样本序列：X2 = S1[t+1:t+k+1] (紧跟锚序列的下一段)
            if t+k+2 <= len(stock_data):
                positive_seq = stock_data[t+1:t+k+1]
                positive_next_day = stock_data[t+k+1]

                # 按专利要求计算价格和成交量变化同向性标签
                price_label = get_price_direction_label(
                    anchor_seq, positive_seq,
                    anchor_next_day[3], positive_next_day[3]  # 收盘价
                )
                volume_label = get_volume_direction_label(
                    anchor_seq, positive_seq,
                    anchor_next_day[4], positive_next_day[4]  # 成交量
                )

                pairs.append({
                    'anchor': np.array(anchor_seq),
                    'sample': np.array(positive_seq),
                    'positive_negative': 1,  # 正样本
                    'price_direction': price_label,
                    'volume_direction': volume_label,
                    'stock_idx': stock_idx,
                    'time_idx': t
                })

            # 时序负样本：X3 = S1[t+m:t+m+k] (同股票较远时刻)
            if t+m+k+1 <= len(stock_data):
                temporal_negative_seq = stock_data[t+m:t+m+k]
                temporal_next_day = stock_data[t+m+k]

                price_label = get_price_direction_label(
                    anchor_seq, temporal_negative_seq,
                    anchor_next_day[3], temporal_next_day[3]  # 收盘价
                )
                volume_label = get_volume_direction_label(
                    anchor_seq, temporal_negative_seq,
                    anchor_next_day[4], temporal_next_day[4]  # 成交量
                )

                pairs.append({
                    'anchor': np.array(anchor_seq),
                    'sample': np.array(temporal_negative_seq),
                    'positive_negative': 0,  # 负样本
                    'price_direction': price_label,
                    'volume_direction': volume_label,
                    'stock_idx': stock_idx,
                    'time_idx': t
                })

            # 对比负样本：X4 = S2[t-k+1:t+1] (其他股票同时期)
            for other_stock_idx, other_stock_data in enumerate(stock_data_list):
                if other_stock_idx != stock_idx and len(other_stock_data) > t+1:
                    if t+1 < len(other_stock_data):
                        contrast_negative_seq = other_stock_data[t-k+1:t+1]
                        contrast_next_day = other_stock_data[t+1]

                        price_label = get_price_direction_label(
                            anchor_seq, contrast_negative_seq,
                            anchor_next_day[3], contrast_next_day[3]  # 收盘价
                        )
                        volume_label = get_volume_direction_label(
                            anchor_seq, contrast_negative_seq,
                            anchor_next_day[4], contrast_next_day[4]  # 成交量
                        )

                        pairs.append({
                            'anchor': np.array(anchor_seq),
                            'sample': np.array(contrast_negative_seq),
                            'positive_negative': 0,  # 负样本
                            'price_direction': price_label,
                            'volume_direction': volume_label,
                            'stock_idx': stock_idx,
                            'time_idx': t
                        })
                        break  # 只取一个对比负样本

    return pairs

def get_price_direction_label(anchor_seq, sample_seq, anchor_next_day_price, sample_next_day_price):
    """
    计算价格变化同向性标签（专利步骤4）
    按专利要求：基于锚序列和样本序列下一个交易日的价格涨跌变化同向性

    Args:
        anchor_seq: 锚序列 [[最高价, 最低价, 开盘价, 收盘价, 成交量, 成交金额], ...]
        sample_seq: 样本序列
        anchor_next_day_price: 锚序列下一个交易日的收盘价
        sample_next_day_price: 样本序列下一个交易日的收盘价

    Returns:
        label: 1表示同向，0表示异向
    """
    # 按专利要求：锚序列下一个交易日的价格变化
    anchor_price_change = anchor_next_day_price - anchor_seq[-1][3]  # 下一日收盘价 - 当前收盘价

    # 样本序列下一个交易日的价格变化
    sample_price_change = sample_next_day_price - sample_seq[-1][3]  # 下一日收盘价 - 当前收盘价

    # 同向为1，异向为0（专利公式：如果两个变化符号相同则为1）
    return 1 if (anchor_price_change * sample_price_change) >= 0 else 0

def get_volume_direction_label(anchor_seq, sample_seq, anchor_next_day_volume, sample_next_day_volume):
    """
    计算成交量变化同向性标签（专利步骤5）
    按专利要求：基于锚序列和样本序列下一个交易日的成交量涨跌变化同向性

    Args:
        anchor_seq: 锚序列
        sample_seq: 样本序列
        anchor_next_day_volume: 锚序列下一个交易日的成交量
        sample_next_day_volume: 样本序列下一个交易日的成交量

    Returns:
        label: 1表示同向，0表示异向
    """
    # 按专利要求：锚序列下一个交易日的成交量变化
    anchor_volume_change = anchor_next_day_volume - anchor_seq[-1][4]  # 下一日成交量 - 当前成交量

    # 样本序列下一个交易日的成交量变化
    sample_volume_change = sample_next_day_volume - sample_seq[-1][4]  # 下一日成交量 - 当前成交量

    # 同向为1，异向为0（专利公式：如果两个变化符号相同则为1）
    return 1 if (anchor_volume_change * sample_volume_change) >= 0 else 0

class PatentDataset(Dataset):
    """专利要求的数据集：支持自监督预训练和LSTM训练"""
    def __init__(self, data, mode='pretrain', k=5):
        self.data = data
        self.mode = mode
        self.k = k

        if mode == 'pretrain':
            # 自监督预训练模式：使用锚-样本序列对
            self.pairs = data  # data应该是锚-样本序列对列表
        else:
            # LSTM训练模式：使用序列-标签对
            self.sequences = data['sequences']
            self.labels = data['labels']

    def __len__(self):
        if self.mode == 'pretrain':
            return len(self.pairs)
        else:
            return len(self.sequences)

    def __getitem__(self, idx):
        if self.mode == 'pretrain':
            pair = self.pairs[idx]
            return {
                'anchor': torch.FloatTensor(pair['anchor']),
                'sample': torch.FloatTensor(pair['sample']),
                'labels': {
                    'positive_negative': torch.LongTensor([pair['positive_negative']]),
                    'price_direction': torch.LongTensor([pair['price_direction']]),
                    'volume_direction': torch.LongTensor([pair['volume_direction']])
                }
            }
        else:
            return {
                'sequence': torch.FloatTensor(self.sequences[idx]),
                'label': torch.LongTensor([self.labels[idx]])
            }

# ============================================================================
# 专利要求的完整训练流程
# ============================================================================

def patent_train_model(kline_data_list, k=5, epochs_pretrain=10, epochs_finetune=5,
                      model_path='patent_model.pth', alpha=1.0, beta=1.0, gamma=1.0,
                      early_stopping=True, patience=10, min_delta=1e-4,
                      pretrained_model_path=None, inherit_epochs=False,
                      start_epoch_pretrain=0, start_epoch_finetune=0):
    """
    按专利CN113159945A要求的完整两阶段训练
    支持加载预训练模型继续训练

    Args:
        kline_data_list: K线数据列表
        k: 序列长度
        epochs_pretrain: 自监督预训练轮数
        epochs_finetune: LSTM微调轮数
        model_path: 模型保存路径
        alpha, beta, gamma: 三个任务的权重系数
        early_stopping: 是否启用早停机制
        patience: 早停耐心值（多少轮无改善后停止）
        min_delta: 最小改善阈值
        pretrained_model_path: 预训练模型路径（可选，用于继续训练）
        inherit_epochs: 是否继承加载模型的训练轮数（仅在pretrained_model_path不为None时有效）
        start_epoch_pretrain: 自监督预训练的起始轮数（当inherit_epochs=False时使用）
        start_epoch_finetune: LSTM微调的起始轮数（当inherit_epochs=False时使用）

    Returns:
        model: 训练好的模型
        scaler: 数据标准化器

    注意：
        - 当使用不同数据继续训练时，建议设置inherit_epochs=False，从轮数0开始
        - 当使用相同数据继续训练时，可以设置inherit_epochs=True，继承之前的轮数
    """
    print("=" * 80)
    print("基于多任务自监督学习的股票涨跌预测方法")
    print("严格按照专利CN113159945A实现")
    print("=" * 80)

    # 步骤1：数据收集和预处理
    print(f"\n步骤1：数据收集和预处理")
    print(f"- K线数据条数：{len(kline_data_list)}")
    print(f"- 序列长度k：{k}天")

    # 提取专利要求的6维特征
    all_stock_data = []
    for kline_data in kline_data_list:
        stock_features = []
        for kline in kline_data:
            features = extract_patent_features(kline)
            stock_features.append(features)
        all_stock_data.append(stock_features)

    print(f"- 提取6维特征：最高价、最低价、开盘价、收盘价、成交量、成交金额")

    # 预训练模型加载（在数据标准化之前）
    loaded_scaler = None
    loaded_epochs_info = {'pretrain': 0, 'finetune': 0}
    model_config = {
        'input_dim': 6,
        'd_model': 128,
        'n_heads': 4,
        'n_layers': 3
    }

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    if pretrained_model_path is not None and os.path.exists(pretrained_model_path):
        print(f"🔄 加载预训练模型：{pretrained_model_path}")
        try:
            checkpoint = torch.load(pretrained_model_path, map_location='cpu', weights_only=False)

            # 检查模型配置兼容性
            if 'model_config' in checkpoint:
                loaded_config = checkpoint['model_config']
                if loaded_config != model_config:
                    print(f"⚠️  警告：预训练模型配置与当前配置不同")
                    print(f"   预训练模型配置：{loaded_config}")
                    print(f"   当前配置：{model_config}")
                    print(f"   将使用预训练模型的配置")
                    model_config = loaded_config

            # 加载数据标准化器（如果存在且兼容）
            if 'scaler' in checkpoint:
                loaded_scaler = checkpoint['scaler']
                print(f"✅ 加载了预训练模型的数据标准化器")

            # 加载训练轮数信息（如果存在）
            if inherit_epochs and 'epochs_info' in checkpoint:
                loaded_epochs_info = checkpoint['epochs_info']
                print(f"✅ 继承训练轮数：预训练{loaded_epochs_info['pretrain']}轮，微调{loaded_epochs_info['finetune']}轮")
            elif inherit_epochs:
                print(f"⚠️  预训练模型中没有轮数信息，将从指定的起始轮数开始")

            print(f"✅ 预训练模型信息加载成功")

        except Exception as e:
            print(f"❌ 加载预训练模型失败：{e}")
            print(f"   将创建新模型进行训练")
            loaded_scaler = None
            loaded_epochs_info = {'pretrain': 0, 'finetune': 0}
    else:
        if pretrained_model_path is not None:
            print(f"⚠️  预训练模型文件不存在：{pretrained_model_path}")
            print(f"   将创建新模型进行训练")

    # 数据标准化
    if loaded_scaler is not None:
        print(f"- 使用预训练模型的数据标准化器")
        scaler = loaded_scaler

        # 检查数据兼容性（可选）
        try:
            # 测试标准化器是否兼容当前数据
            test_sample = all_stock_data[0][0] if all_stock_data and all_stock_data[0] else [0] * 6
            scaler.transform([test_sample])
            print(f"✅ 预训练标准化器与当前数据兼容")
        except Exception as e:
            print(f"⚠️  预训练标准化器与当前数据不兼容：{e}")
            print(f"   将重新训练标准化器")
            scaler = StandardScaler()
            all_data_flat = []
            for stock_data in all_stock_data:
                all_data_flat.extend(stock_data)
            scaler.fit(all_data_flat)
    else:
        print(f"- 创建新的数据标准化器")
        scaler = StandardScaler()
        all_data_flat = []
        for stock_data in all_stock_data:
            all_data_flat.extend(stock_data)
        scaler.fit(all_data_flat)

    # 标准化每只股票的数据
    normalized_stock_data = []
    for stock_data in all_stock_data:
        normalized_data = scaler.transform(stock_data)
        normalized_stock_data.append(normalized_data.tolist())

    # 数据集划分：如果股票数量少，直接使用全部数据训练
    if len(normalized_stock_data) >= 3:
        train_data, temp_data = train_test_split(normalized_stock_data, test_size=0.3, random_state=42)
        if len(temp_data) >= 2:
            val_data, test_data = train_test_split(temp_data, test_size=0.5, random_state=42)
        else:
            val_data = temp_data
            test_data = temp_data
    else:
        # 股票数量太少，全部用于训练
        train_data = normalized_stock_data
        val_data = normalized_stock_data
        test_data = normalized_stock_data

    print(f"- 数据集划分：训练集{len(train_data)}只股票，验证集{len(val_data)}只股票，测试集{len(test_data)}只股票")

    # 步骤2：锚-样本序列对采样
    print(f"\n步骤2：锚-样本序列对采样")
    anchor_sample_pairs = create_patent_anchor_sample_pairs(train_data, k=k, m=10)
    print(f"- 生成锚-样本序列对：{len(anchor_sample_pairs)}对")

    # 创建自监督预训练数据集
    pretrain_dataset = PatentDataset(anchor_sample_pairs, mode='pretrain', k=k)

    # 根据设备调整batch size
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    if device.type == 'cuda':
        # GPU训练使用更大的batch size
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        if gpu_memory >= 12:
            pretrain_batch_size = 32
        elif gpu_memory >= 8:
            pretrain_batch_size = 24
        else:
            pretrain_batch_size = 16
    else:
        pretrain_batch_size = 16
    batch_size_mul = 0
    for i in range(batch_size_mul):
        pretrain_batch_size *= 2
    pretrain_loader = DataLoader(pretrain_dataset, batch_size=pretrain_batch_size, shuffle=True)

    # 步骤3-6：自监督预训练
    print(f"\n步骤3-6：自监督预训练（三个辅助任务联合训练）")
    print(f"- 使用设备：{device}")
    if device.type == 'cuda':
        print(f"- GPU显存：{torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        print(f"- 预训练batch size：{pretrain_batch_size}")

    # 模型初始化
    if pretrained_model_path is not None and os.path.exists(pretrained_model_path):
        # 创建模型并加载权重
        model = PatentStockPredictor(**model_config).to(device)
        checkpoint = torch.load(pretrained_model_path, map_location='cpu', weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"✅ 预训练模型权重加载成功")
    else:
        # 创建新模型
        model = PatentStockPredictor(**model_config).to(device)

    print(f"- 模型参数数量：{sum(p.numel() for p in model.parameters()):,}")

    # 自监督预训练
    # TODO: 调整学习率，默认0.001
    lr = 0.000001
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    model.train()

    # 确定实际的起始轮数
    actual_start_epoch_pretrain = start_epoch_pretrain
    actual_start_epoch_finetune = start_epoch_finetune

    if inherit_epochs and pretrained_model_path is not None:
        actual_start_epoch_pretrain = loaded_epochs_info['pretrain']
        actual_start_epoch_finetune = loaded_epochs_info['finetune']
        print(f"- 继承轮数：预训练从第{actual_start_epoch_pretrain}轮开始，微调从第{actual_start_epoch_finetune}轮开始")
    else:
        print(f"- 自定义轮数：预训练从第{actual_start_epoch_pretrain}轮开始，微调从第{actual_start_epoch_finetune}轮开始")

    # 早停机制变量
    best_pretrain_loss = float('inf')
    pretrain_patience_counter = 0
    best_pretrain_model_state = None

    print(f"- 开始自监督预训练...")
    if early_stopping:
        print(f"- 启用早停机制：耐心值={patience}, 最小改善阈值={min_delta}")

    for epoch in range(actual_start_epoch_pretrain, actual_start_epoch_pretrain + epochs_pretrain):
        total_loss = 0
        pos_neg_loss_sum = 0
        price_loss_sum = 0
        volume_loss_sum = 0

        for batch_data in pretrain_loader:
            anchor_seq = batch_data['anchor'].to(device)
            sample_seq = batch_data['sample'].to(device)

            # 准备任务标签
            task_labels = {}
            for task_name in ['positive_negative', 'price_direction', 'volume_direction']:
                task_labels[task_name] = batch_data['labels'][task_name].to(device).squeeze()

            optimizer.zero_grad()

            # 前向传播
            ssl_losses = model(anchor_seq, sample_seq, task_labels, mode='pretrain')

            # 按专利要求线性组合损失
            total_batch_loss = (alpha * ssl_losses.get('positive_negative_loss', 0) +
                              beta * ssl_losses.get('price_direction_loss', 0) +
                              gamma * ssl_losses.get('volume_direction_loss', 0))

            # 反向传播
            total_batch_loss.backward()
            optimizer.step()

            total_loss += total_batch_loss.item()
            pos_neg_loss_sum += ssl_losses.get('positive_negative_loss', 0).item()
            price_loss_sum += ssl_losses.get('price_direction_loss', 0).item()
            volume_loss_sum += ssl_losses.get('volume_direction_loss', 0).item()

        avg_loss = total_loss / len(pretrain_loader)
        avg_pos_neg = pos_neg_loss_sum / len(pretrain_loader)
        avg_price = price_loss_sum / len(pretrain_loader)
        avg_volume = volume_loss_sum / len(pretrain_loader)

        current_epoch_display = epoch + 1
        total_epochs_display = actual_start_epoch_pretrain + epochs_pretrain
        current_lr = optimizer.param_groups[0]['lr']
        print(f"  Epoch {current_epoch_display}/{total_epochs_display}: 总损失={avg_loss:.5f}, "
              f"正负样本={avg_pos_neg:.5f}, 价格同向={avg_price:.5f}, 成交量同向={avg_volume:.5f}, 学习率={current_lr:.6f}")

        # 早停检查
        if early_stopping:
            if avg_loss < best_pretrain_loss - min_delta:
                best_pretrain_loss = avg_loss
                pretrain_patience_counter = 0
                # 保存当前最佳模型状态
                best_pretrain_model_state = model.state_dict().copy()
                print(f"    ✓ 损失改善，重置耐心计数器，保存最佳模型")
            else:
                pretrain_patience_counter += 1
                print(f"    - 损失无改善，耐心计数器: {pretrain_patience_counter}/{patience}")

                if pretrain_patience_counter >= patience:
                    print(f"    🛑 早停触发！自监督预训练在第{current_epoch_display}轮停止")
                    break

    print(f"✅ 自监督预训练完成！")

    # 恢复最佳预训练模型
    if early_stopping and best_pretrain_model_state is not None:
        model.load_state_dict(best_pretrain_model_state)
        print(f"✅ 已恢复最佳预训练模型（损失: {best_pretrain_loss:.5f}）")

    # 步骤7：提取序列编码器，编码得到表征因子
    print(f"\n步骤7：提取序列编码器，编码得到表征因子")
    model.eval()

    # 准备LSTM训练数据
    lstm_train_sequences = []
    lstm_train_labels = []

    # 从训练数据中创建序列-标签对
    for stock_data in train_data:
        if len(stock_data) < k + 1:
            continue

        for i in range(len(stock_data) - k):
            sequence = stock_data[i:i+k]

            # 标签：下一日的价格涨跌
            current_close = stock_data[i+k-1][3]  # 当前收盘价
            next_close = stock_data[i+k][3]       # 下一日收盘价
            label = 1 if next_close > current_close else 0

            lstm_train_sequences.append(sequence)
            lstm_train_labels.append(label)

    print(f"- 生成LSTM训练序列：{len(lstm_train_sequences)}个")

    # 使用预训练编码器提取表征因子
    train_embeddings = []
    with torch.no_grad():
        for sequence in lstm_train_sequences:
            seq_tensor = torch.FloatTensor(sequence).unsqueeze(0).to(device)
            embedding = model(seq_tensor, mode='extract_features')
            train_embeddings.append(embedding.cpu())

    train_embeddings = torch.cat(train_embeddings, dim=0)
    print(f"- 提取表征因子完成，维度：{train_embeddings.shape}")

    # 步骤8：基于LSTM的股票涨跌预测训练
    print(f"\n步骤8：基于LSTM的股票涨跌预测训练")

    # 创建LSTM训练数据集
    lstm_dataset = TensorDataset(train_embeddings, torch.LongTensor(lstm_train_labels))

    # 根据设备调整LSTM batch size
    if device.type == 'cuda':
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        if gpu_memory >= 12:
            lstm_batch_size = 64
        elif gpu_memory >= 8:
            lstm_batch_size = 48
        else:
            lstm_batch_size = 32
    else:
        lstm_batch_size = 32
    for i in range(batch_size_mul):
        lstm_batch_size *= 2
    lstm_loader = DataLoader(lstm_dataset, batch_size=lstm_batch_size, shuffle=True)

    # LSTM训练
    lstm_optimizer = torch.optim.Adam(model.lstm_predictor.parameters(), lr=lr)
    criterion = nn.CrossEntropyLoss()

    # 早停机制变量（LSTM阶段）
    best_lstm_loss = float('inf')
    lstm_patience_counter = 0
    best_lstm_model_state = None

    model.lstm_predictor.train()
    print(f"- 开始LSTM预测训练...")
    if device.type == 'cuda':
        print(f"- LSTM batch size：{lstm_batch_size}")
    if early_stopping:
        print(f"- LSTM阶段早停机制：耐心值={patience}, 最小改善阈值={min_delta}")

    for epoch in range(actual_start_epoch_finetune, actual_start_epoch_finetune + epochs_finetune):
        total_loss = 0
        correct = 0
        total = 0

        for embeddings_batch, labels_batch in lstm_loader:
            embeddings_batch = embeddings_batch.to(device)
            labels_batch = labels_batch.to(device)

            # 扩展维度用于LSTM (batch_size, 1, d_model)
            embeddings_batch = embeddings_batch.unsqueeze(1)

            lstm_optimizer.zero_grad()

            # 前向传播
            predictions = model(embeddings_batch, mode='predict')
            loss = criterion(predictions, labels_batch)

            # 反向传播
            loss.backward()
            lstm_optimizer.step()

            total_loss += loss.item()

            # 计算准确率
            _, predicted = torch.max(predictions.data, 1)
            total += labels_batch.size(0)
            correct += (predicted == labels_batch).sum().item()

        avg_loss = total_loss / len(lstm_loader)
        accuracy = 100 * correct / total
        current_epoch_display_lstm = epoch + 1
        total_epochs_display_lstm = actual_start_epoch_finetune + epochs_finetune
        current_lr = lstm_optimizer.param_groups[0]['lr']
        print(f"  Epoch {current_epoch_display_lstm}/{total_epochs_display_lstm}: 损失={avg_loss:.4f}, 准确率={accuracy:.2f}%, 学习率={current_lr}")

        # 早停检查（LSTM阶段）
        if early_stopping:
            if avg_loss < best_lstm_loss - min_delta:
                best_lstm_loss = avg_loss
                lstm_patience_counter = 0
                # 保存当前最佳LSTM模型状态
                best_lstm_model_state = model.lstm_predictor.state_dict().copy()
                print(f"    ✓ LSTM损失改善，重置耐心计数器，保存最佳LSTM模型")
            else:
                lstm_patience_counter += 1
                print(f"    - LSTM损失无改善，耐心计数器: {lstm_patience_counter}/{patience}")

                if lstm_patience_counter >= patience:
                    print(f"    🛑 LSTM早停触发！训练在第{current_epoch_display_lstm}轮停止")
                    break

    print(f"✅ LSTM预测训练完成！")

    # 恢复最佳LSTM模型
    if early_stopping and best_lstm_model_state is not None:
        model.lstm_predictor.load_state_dict(best_lstm_model_state)
        print(f"✅ 已恢复最佳LSTM模型（损失: {best_lstm_loss:.4f}）")

    # 保存模型（包含训练轮数信息）
    final_epochs_info = {
        'pretrain': actual_start_epoch_pretrain + epochs_pretrain,
        'finetune': actual_start_epoch_finetune + epochs_finetune
    }

    torch.save({
        'model_state_dict': model.state_dict(),
        'scaler': scaler,
        'k': k,
        'model_config': model_config,
        'epochs_info': final_epochs_info,
        'training_params': {
            'alpha': alpha,
            'beta': beta,
            'gamma': gamma,
            'early_stopping': early_stopping,
            'patience': patience,
            'min_delta': min_delta
        }
    }, model_path)
    print(f"✅ 模型已保存到：{model_path}")

    print(f"\n" + "=" * 80)
    print(f"🎉 专利CN113159945A完整实现训练完成！")
    print(f"✅ 步骤1-2：数据预处理和锚-样本对采样")
    print(f"✅ 步骤3-6：三个自监督辅助任务联合训练")
    print(f"✅ 步骤7-8：序列编码器提取和LSTM预测训练")
    print(f"=" * 80)

    return model, scaler

def patent_predict(kline_data, model_path='patent_model.pth'):
    """
    按专利要求进行股票涨跌预测（步骤9）

    Args:
        kline_data: K线数据列表，包含k天的数据
        model_path: 模型文件路径

    Returns:
        result: 预测结果字典
    """
    print("=" * 60)
    print("专利CN113159945A股票涨跌预测")
    print("=" * 60)

    # 加载模型
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")

    checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
    scaler = checkpoint['scaler']
    k = checkpoint['k']
    model_config = checkpoint['model_config']

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = PatentStockPredictor(**model_config).to(device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()

    print(f"✅ 模型加载成功，序列长度k={k}")

    # 检查数据长度
    if len(kline_data) < k:
        raise ValueError(f"需要至少{k}天的K线数据，当前只有{len(kline_data)}天")

    # 数据预处理
    print(f"处理{len(kline_data)}天的K线数据...")

    # 提取专利要求的6维特征
    features_list = []
    for kline in kline_data[-k:]:  # 取最后k天的数据
        features = extract_patent_features(kline)
        features_list.append(features)

    sequence = np.array(features_list)

    # 标准化
    sequence_scaled = scaler.transform(sequence)

    # 预测
    print("进行预测...")
    with torch.no_grad():
        # 步骤7：提取表征因子
        sequence_tensor = torch.FloatTensor(sequence_scaled).unsqueeze(0).to(device)
        embedding = model(sequence_tensor, mode='extract_features')

        # 步骤8：LSTM预测
        embedding_expanded = embedding.unsqueeze(1)  # (1, 1, d_model)
        prediction = model(embedding_expanded, mode='predict')
        probabilities = torch.softmax(prediction, dim=1)[0]

        up_probability = probabilities[1].item()
        predicted_label = 1 if up_probability > 0.5 else 0

    # 置信度评估
    confidence_score = abs(up_probability - 0.5) * 2
    if confidence_score > 0.8:
        confidence = "高"
    elif confidence_score > 0.6:
        confidence = "中"
    else:
        confidence = "低"

    result = {
        'prediction': predicted_label,
        'probability': up_probability,
        'confidence': confidence,
        'confidence_score': confidence_score
    }

    print(f"\n步骤9：预测结果")
    print(f"📈 预测趋势: {'上涨' if predicted_label == 1 else '下跌'}")
    print(f"📊 上涨概率: {up_probability:.1%}")
    print(f"🎯 置信度: {confidence} ({confidence_score:.2f})")
    print("=" * 60)

    return result

# ============================================================================
# 早停机制辅助函数
# ============================================================================

def get_recommended_early_stopping_params(num_stocks, days_per_stock):
    """
    根据数据量推荐早停参数

    Args:
        num_stocks: 股票数量
        days_per_stock: 每只股票的天数

    Returns:
        dict: 推荐的早停参数
    """
    total_samples = num_stocks * days_per_stock

    if total_samples < 1000:
        # 小数据集：更激进的早停
        return {
            'epochs_pretrain': 30,
            'epochs_finetune': 15,
            'patience': 3,
            'min_delta': 1e-3
        }
    elif total_samples < 5000:
        # 中等数据集
        return {
            'epochs_pretrain': 60,
            'epochs_finetune': 25,
            'patience': 5,
            'min_delta': 1e-4
        }
    elif total_samples < 20000:
        # 大数据集
        return {
            'epochs_pretrain': 100,
            'epochs_finetune': 40,
            'patience': 8,
            'min_delta': 1e-4
        }
    else:
        # 超大数据集：更保守的早停
        return {
            'epochs_pretrain': 200,
            'epochs_finetune': 50,
            'patience': 12,
            'min_delta': 1e-5
        }

# ============================================================================
# 继续训练辅助函数
# ============================================================================

def patent_continue_training(kline_data_list, pretrained_model_path,
                            additional_epochs_pretrain=5, additional_epochs_finetune=3,
                            inherit_epochs=True, model_path='patent_model_continued.pth',
                            **kwargs):
    """
    继续训练已保存的模型

    Args:
        kline_data_list: K线数据列表（可以是新数据或原数据）
        pretrained_model_path: 预训练模型路径
        additional_epochs_pretrain: 额外的自监督预训练轮数
        additional_epochs_finetune: 额外的LSTM微调轮数
        inherit_epochs: 是否继承原模型的训练轮数
        model_path: 新模型保存路径
        **kwargs: 其他训练参数

    Returns:
        model: 继续训练后的模型
        scaler: 数据标准化器
    """
    print("=" * 80)
    print("🔄 继续训练已保存的模型")
    print("=" * 80)

    if not os.path.exists(pretrained_model_path):
        raise FileNotFoundError(f"预训练模型文件不存在: {pretrained_model_path}")

    # 加载原模型信息
    checkpoint = torch.load(pretrained_model_path, map_location='cpu', weights_only=False)
    if 'epochs_info' in checkpoint:
        original_epochs = checkpoint['epochs_info']
        print(f"📊 原模型训练轮数：预训练{original_epochs['pretrain']}轮，微调{original_epochs['finetune']}轮")
    else:
        print(f"⚠️  原模型没有轮数信息，将从0开始计算")

    # 调用主训练函数
    return patent_train_model(
        kline_data_list=kline_data_list,
        epochs_pretrain=additional_epochs_pretrain,
        epochs_finetune=additional_epochs_finetune,
        model_path=model_path,
        pretrained_model_path=pretrained_model_path,
        inherit_epochs=inherit_epochs,
        **kwargs
    )

def patent_train_with_new_data(kline_data_list, pretrained_model_path,
                              epochs_pretrain=10, epochs_finetune=5,
                              model_path='patent_model_new_data.pth',
                              **kwargs):
    """
    使用新数据继续训练模型（推荐不继承轮数）

    Args:
        kline_data_list: 新的K线数据列表
        pretrained_model_path: 预训练模型路径
        epochs_pretrain: 在新数据上的预训练轮数
        epochs_finetune: 在新数据上的微调轮数
        model_path: 新模型保存路径
        **kwargs: 其他训练参数

    Returns:
        model: 训练后的模型
        scaler: 数据标准化器
    """
    print("=" * 80)
    print("🆕 使用新数据继续训练模型")
    print("=" * 80)

    return patent_train_model(
        kline_data_list=kline_data_list,
        epochs_pretrain=epochs_pretrain,
        epochs_finetune=epochs_finetune,
        model_path=model_path,
        pretrained_model_path=pretrained_model_path,
        inherit_epochs=False,  # 新数据不继承轮数
        start_epoch_pretrain=0,
        start_epoch_finetune=0,
        **kwargs
    )

# ============================================================================
# 示例用法和测试
# ============================================================================

def generate_sample_data(num_stocks=3, days_per_stock=100, k=5):
    """生成示例K线数据用于测试"""
    sample_data = []

    for stock_idx in range(num_stocks):
        stock_klines = []
        base_price = 100.0 + stock_idx * 10  # 不同股票不同基础价格

        for day in range(days_per_stock):
            # 模拟价格波动
            change = np.random.normal(0, 0.02)
            new_price = base_price * (1 + change)

            open_price = base_price
            close_price = new_price
            high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.01))
            low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.01))
            volume = np.random.uniform(1000000, 5000000)
            turnover = volume * (high_price + low_price) / 2

            kline = [
                1640995200000 + day * 86400000,  # 开盘时间
                f"{open_price:.6f}",             # 开盘价
                f"{high_price:.6f}",             # 最高价
                f"{low_price:.6f}",              # 最低价
                f"{close_price:.6f}",            # 收盘价
                f"{volume:.8f}",                 # 成交量
                1640995200000 + day * 86400000 + 86399999,  # 收盘时间
                f"{turnover:.8f}",               # 成交额
                308,                             # 成交笔数
                f"{volume*0.6:.8f}",            # 主动买入成交量
                f"{turnover*0.6:.8f}",          # 主动买入成交额
                "0"                             # 忽略参数
            ]

            stock_klines.append(kline)
            base_price = new_price

        sample_data.append(stock_klines)

    return sample_data

'''
if __name__ == "__main__":
    print("=" * 80)
    print("专利CN113159945A完整实现测试（带早停机制）")
    print("=" * 80)

    # 生成示例数据进行测试
    # print("生成示例数据...")
    # num_stocks = 3
    # days_per_stock = 50
    # sample_kline_data = generate_sample_data(num_stocks=num_stocks, days_per_stock=days_per_stock, k=5)
    # print(f"生成了{len(sample_kline_data)}只股票的数据，每只股票{len(sample_kline_data[0])}天")

    # # 获取推荐的早停参数
    # recommended_params = get_recommended_early_stopping_params(num_stocks, days_per_stock)
    # print(f"推荐的训练参数: {recommended_params}")

    # 如果有真实数据文件，可以使用以下代码
    symbols = ["BTCUSDT", "ETHUSDT", "SOLUSDT"]
    interval = "30m"
    sample_kline_data = []
    for symbol in symbols:
        input_filename = f"{symbol}_{interval}.json"
        try:
            with open(input_filename, 'r') as f:
                klines_data = json.load(f)
                sample_kline_data.append(klines_data)
        except FileNotFoundError:
            print(f"文件 {input_filename} 不存在，使用示例数据")
            break
    num_stocks = len(sample_kline_data)
    days_per_stock = len(sample_kline_data[0])
    # 获取推荐的早停参数
    recommended_params = get_recommended_early_stopping_params(num_stocks, days_per_stock)
    print(f"推荐的训练参数: {recommended_params}")

    # 完整两阶段训练
    print("\n开始专利完整训练...")
    try:
        model, scaler = patent_train_model(
            kline_data_list=sample_kline_data,
            k=10,
            epochs_pretrain=recommended_params['epochs_pretrain'],
            epochs_finetune=recommended_params['epochs_finetune'],
            model_path='patent_model.pth',
            early_stopping=True,  # 启用早停
            patience=recommended_params['patience'],
            min_delta=recommended_params['min_delta']
        )

        # 预测测试
        print("\n开始预测测试...")
        prediction_data = sample_kline_data[0][-10:]  # 第一只股票的最后5天数据
        result = patent_predict(prediction_data, model_path='patent_model.pth')

        print(f"\n最终预测结果: {result}")
        print(f"\n🎉 初始训练完成！")

        # 继续训练示例
        print("\n" + "=" * 80)
        print("🔄 继续训练示例")
        print("=" * 80)

        # 示例1：使用相同数据继续训练（继承轮数）
        print("\n示例1：使用相同数据继续训练（继承轮数）")
        try:
            continued_model, continued_scaler = patent_continue_training(
                kline_data_list=sample_kline_data,
                pretrained_model_path='patent_model.pth',
                additional_epochs_pretrain=3,
                additional_epochs_finetune=2,
                inherit_epochs=True,
                model_path='patent_model_continued.pth',
                early_stopping=True,
                patience=3
            )
            print("✅ 继续训练完成！")
        except Exception as e:
            print(f"❌ 继续训练失败: {e}")

        # 示例2：使用新数据训练（不继承轮数）
        model, scaler = patent_train_with_new_data(
            kline_data_list=sample_kline_data,
            pretrained_model_path='patent_model.pth',
            k=10,
            epochs_pretrain=recommended_params['epochs_pretrain'],
            epochs_finetune=recommended_params['epochs_finetune'],
            model_path='patent_model_new_data.pth',
            early_stopping=True,
            patience=recommended_params['patience'],
            min_delta=recommended_params['min_delta'],
        )

    except Exception as e:
        print(f"❌ 训练或预测过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
'''

if __name__ == "__main__":
    print("=" * 80)
    print("专利CN113159945A完整实现测试（带早停机制）")
    print("=" * 80)

    # 生成示例数据进行测试
    # print("生成示例数据...")
    # num_stocks = 3
    # days_per_stock = 50
    # sample_kline_data = generate_sample_data(num_stocks=num_stocks, days_per_stock=days_per_stock, k=5)
    # print(f"生成了{len(sample_kline_data)}只股票的数据，每只股票{len(sample_kline_data[0])}天")

    # # 获取推荐的早停参数
    # recommended_params = get_recommended_early_stopping_params(num_stocks, days_per_stock)
    # print(f"推荐的训练参数: {recommended_params}")

    # 如果有真实数据文件，可以使用以下代码
    symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "SOLUSDT", "ADAUSDT", "XRPUSDT", "DOGEUSDT", "AVAXUSDT", "LINKUSDT", "LTCUSDT"]
    interval = "30m"
    sample_kline_data = []
    for symbol in symbols:
        input_filename = f"{symbol}_{interval}.json"
        try:
            with open(input_filename, 'r') as f:
                klines_data = json.load(f)
                sample_kline_data.append(klines_data)
        except FileNotFoundError:
            print(f"文件 {input_filename} 不存在，使用示例数据")
            break
    num_stocks = len(sample_kline_data)
    days_per_stock = len(sample_kline_data[0])
    # 获取推荐的早停参数
    recommended_params = get_recommended_early_stopping_params(num_stocks, days_per_stock)
    print(f"推荐的训练参数: {recommended_params}")

    # 完整两阶段训练
    print("\n开始专利完整训练...")
    try:
        model, scaler = patent_train_with_new_data(
            kline_data_list=sample_kline_data,
            pretrained_model_path='patent_model.pth',
            k=10,
            epochs_pretrain=recommended_params['epochs_pretrain'],
            epochs_finetune=recommended_params['epochs_finetune'],
            model_path='patent_model_new_data.pth',
            early_stopping=True,
            patience=recommended_params['patience'],
            min_delta=recommended_params['min_delta'],
        )

        # 预测测试
        print("\n开始预测测试...")
        prediction_data = sample_kline_data[0][-10:]  # 第一只股票的最后5天数据
        result = patent_predict(prediction_data, model_path='patent_model.pth')

        print(f"\n最终预测结果: {result}")
        print(f"\n🎉 初始训练完成！")

    except Exception as e:
        print(f"❌ 训练或预测过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
