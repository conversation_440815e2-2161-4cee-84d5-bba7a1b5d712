"""
简化版本的多任务自监督学习股票预测模型
基于专利CN113159945A的核心思想
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')


class SimpleStockEncoder(nn.Module):
    """简化的股票序列编码器"""
    def __init__(self, input_dim, hidden_dim=128, num_layers=2):
        super(SimpleStockEncoder, self).__init__()
        self.hidden_dim = hidden_dim
        
        # 输入投影
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        
        # LSTM编码器
        self.lstm = nn.LSTM(hidden_dim, hidden_dim, num_layers, 
                           batch_first=True, dropout=0.1)
        
        # 自注意力层
        self.self_attention = nn.MultiheadAttention(hidden_dim, num_heads=4, 
                                                   batch_first=True)
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(hidden_dim)
        
    def forward(self, x):
        # x shape: (batch_size, seq_len, input_dim)
        x = self.input_projection(x)
        
        # LSTM编码
        lstm_out, _ = self.lstm(x)
        
        # 自注意力
        attn_out, _ = self.self_attention(lstm_out, lstm_out, lstm_out)
        
        # 残差连接和层归一化
        output = self.layer_norm(lstm_out + attn_out)
        
        return output


class SelfSupervisedTasks(nn.Module):
    """自监督学习任务"""
    def __init__(self, hidden_dim):
        super(SelfSupervisedTasks, self).__init__()
        
        # 任务1: 序列重构
        self.reconstruction_head = nn.Linear(hidden_dim, hidden_dim)
        
        # 任务2: 下一步预测
        self.next_step_head = nn.Linear(hidden_dim, hidden_dim)
        
        # 任务3: 对比学习
        self.contrastive_head = nn.Linear(hidden_dim, 64)
        
    def forward(self, encoded_seq):
        batch_size, seq_len, hidden_dim = encoded_seq.shape
        
        # 任务1: 序列重构损失
        reconstructed = self.reconstruction_head(encoded_seq)
        reconstruction_loss = F.mse_loss(reconstructed, encoded_seq)
        
        # 任务2: 下一步预测损失
        if seq_len > 1:
            next_step_pred = self.next_step_head(encoded_seq[:, :-1, :])
            next_step_loss = F.mse_loss(next_step_pred, encoded_seq[:, 1:, :])
        else:
            next_step_loss = torch.tensor(0.0)
        
        # 任务3: 对比学习特征
        contrastive_features = self.contrastive_head(encoded_seq.mean(dim=1))
        
        return {
            'reconstruction_loss': reconstruction_loss,
            'next_step_loss': next_step_loss,
            'contrastive_features': contrastive_features
        }


class StockPredictionHead(nn.Module):
    """股票预测头"""
    def __init__(self, hidden_dim, num_classes=3):
        super(StockPredictionHead, self).__init__()
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, num_classes)
        )
    
    def forward(self, encoded_seq):
        # 使用最后一个时间步进行预测
        last_hidden = encoded_seq[:, -1, :]
        return self.classifier(last_hidden)


class SimpleStockPredictor(nn.Module):
    """简化的多任务股票预测模型"""
    def __init__(self, input_dim, hidden_dim=128, num_classes=3):
        super(SimpleStockPredictor, self).__init__()
        
        self.encoder = SimpleStockEncoder(input_dim, hidden_dim)
        self.ssl_tasks = SelfSupervisedTasks(hidden_dim)
        self.prediction_head = StockPredictionHead(hidden_dim, num_classes)
    
    def forward(self, x, mode='train'):
        # 编码
        encoded_seq = self.encoder(x)
        
        if mode == 'train':
            # 训练模式：计算自监督损失
            ssl_outputs = self.ssl_tasks(encoded_seq)
            prediction = self.prediction_head(encoded_seq)
            
            return {
                'prediction': prediction,
                'ssl_outputs': ssl_outputs
            }
        else:
            # 推理模式
            prediction = self.prediction_head(encoded_seq)
            return prediction


class StockDataset(Dataset):
    """股票数据集"""
    def __init__(self, data, labels):
        self.data = data
        self.labels = labels

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        sequence = self.data[idx]  # 已经是正确的序列长度
        label = self.labels[idx]
        return torch.FloatTensor(sequence), torch.LongTensor([label])


def create_sample_data(n_samples=1000, seq_len=30):
    """创建示例股票数据"""
    np.random.seed(42)
    
    # 生成基础价格序列
    prices = []
    current_price = 100.0
    
    for _ in range(n_samples + seq_len):
        change = np.random.normal(0, 0.02)
        current_price *= (1 + change)
        prices.append(current_price)
    
    prices = np.array(prices)
    
    # 计算技术指标
    data = []
    labels = []
    
    for i in range(len(prices) - seq_len):
        price_window = prices[i:i + seq_len + 1]
        
        # 模拟OHLCV数据
        features = []
        for j in range(seq_len):
            open_price = price_window[j]
            close_price = price_window[j + 1]
            high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.01))
            low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.01))
            volume = np.random.uniform(1000, 10000)
            
            features.append([open_price, high_price, low_price, close_price, volume])
        
        data.append(features)
        
        # 标签：下一天的涨跌情况
        next_price = price_window[-1]
        current_price = price_window[-2]
        
        if next_price > current_price * 1.01:
            label = 2  # 涨
        elif next_price < current_price * 0.99:
            label = 0  # 跌
        else:
            label = 1  # 平
        
        labels.append(label)
    
    return np.array(data), np.array(labels)


def train_model():
    """训练模型"""
    print("创建示例数据...")
    data, labels = create_sample_data(n_samples=1000, seq_len=30)
    
    # 数据标准化
    scaler = StandardScaler()
    data_reshaped = data.reshape(-1, data.shape[-1])
    data_scaled = scaler.fit_transform(data_reshaped)
    data_scaled = data_scaled.reshape(data.shape)
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        data_scaled, labels, test_size=0.2, random_state=42
    )
    
    # 创建数据集和数据加载器
    train_dataset = StockDataset(X_train, y_train)
    test_dataset = StockDataset(X_test, y_test)
    
    train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=16, shuffle=False)
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = SimpleStockPredictor(
        input_dim=5,  # OHLCV
        hidden_dim=64,
        num_classes=3
    ).to(device)
    
    # 优化器和损失函数
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    criterion = nn.CrossEntropyLoss()
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"使用设备: {device}")
    
    # 训练循环
    model.train()
    num_epochs = 5
    
    for epoch in range(num_epochs):
        total_loss = 0
        correct = 0
        total = 0
        
        for batch_idx, (data_batch, labels_batch) in enumerate(train_loader):
            data_batch = data_batch.to(device)
            labels_batch = labels_batch.squeeze().to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(data_batch, mode='train')
            
            # 主任务损失
            main_loss = criterion(outputs['prediction'], labels_batch)
            
            # 自监督任务损失
            ssl_outputs = outputs['ssl_outputs']
            ssl_loss = (ssl_outputs['reconstruction_loss'] + 
                       ssl_outputs['next_step_loss'])
            
            # 总损失
            total_loss_batch = main_loss + 0.1 * ssl_loss
            
            # 反向传播
            total_loss_batch.backward()
            optimizer.step()
            
            total_loss += total_loss_batch.item()
            
            # 计算准确率
            _, predicted = torch.max(outputs['prediction'].data, 1)
            total += labels_batch.size(0)
            correct += (predicted == labels_batch).sum().item()
            
            if batch_idx % 20 == 0:
                print(f'Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}, '
                      f'Loss: {total_loss_batch.item():.4f}, '
                      f'Acc: {100.*correct/total:.2f}%')
        
        avg_loss = total_loss / len(train_loader)
        accuracy = 100. * correct / total
        print(f'Epoch {epoch+1}/{num_epochs} - Loss: {avg_loss:.4f}, Accuracy: {accuracy:.2f}%')
    
    # 测试模型
    model.eval()
    test_correct = 0
    test_total = 0
    
    with torch.no_grad():
        for data_batch, labels_batch in test_loader:
            data_batch = data_batch.to(device)
            labels_batch = labels_batch.squeeze().to(device)
            
            outputs = model(data_batch, mode='inference')
            _, predicted = torch.max(outputs.data, 1)
            
            test_total += labels_batch.size(0)
            test_correct += (predicted == labels_batch).sum().item()
    
    test_accuracy = 100. * test_correct / test_total
    print(f'测试准确率: {test_accuracy:.2f}%')
    
    # 保存模型
    torch.save(model.state_dict(), 'simple_stock_predictor.pth')
    print("模型已保存为 'simple_stock_predictor.pth'")
    
    return model, scaler


if __name__ == "__main__":
    print("简化版多任务自监督学习股票预测模型")
    print("基于专利CN113159945A")
    print("=" * 50)
    
    # 训练模型
    model, scaler = train_model()
    
    print("\n模型训练完成！")
    print("该模型实现了以下功能：")
    print("1. LSTM + 自注意力的序列编码器")
    print("2. 多个自监督学习任务（序列重构、下一步预测、对比学习）")
    print("3. 股票涨跌三分类预测（涨/跌/平）")
    print("4. 端到端训练的多任务学习框架")
