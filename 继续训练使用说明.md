# 继续训练功能使用说明

## 功能概述

已成功为专利CN113159945A股票预测模型添加了继续训练功能，支持：

1. ✅ **加载已保存模型进行继续训练**
2. ✅ **选择是否继承加载模型的训练轮数**
3. ✅ **支持使用不同数据继续训练**
4. ✅ **灵活的轮数管理**

## 新增参数说明

### `patent_train_model` 函数新增参数：

- `pretrained_model_path`: 预训练模型路径（可选）
- `inherit_epochs`: 是否继承加载模型的训练轮数（默认False）
- `start_epoch_pretrain`: 自监督预训练的起始轮数（默认0）
- `start_epoch_finetune`: LSTM微调的起始轮数（默认0）

## 使用场景和建议

### 场景1：相同数据继续训练（推荐继承轮数）
```python
# 继续训练相同数据，继承轮数
model, scaler = patent_train_model(
    kline_data_list=original_data,
    epochs_pretrain=10,  # 额外训练10轮
    epochs_finetune=5,   # 额外训练5轮
    pretrained_model_path='patent_model.pth',
    inherit_epochs=True,  # 继承原模型轮数
    model_path='patent_model_continued.pth'
)
```

### 场景2：新数据训练（推荐不继承轮数）
```python
# 使用新数据训练，不继承轮数
model, scaler = patent_train_model(
    kline_data_list=new_data,
    epochs_pretrain=15,
    epochs_finetune=8,
    pretrained_model_path='patent_model.pth',
    inherit_epochs=False,  # 不继承轮数，从0开始
    start_epoch_pretrain=0,
    start_epoch_finetune=0,
    model_path='patent_model_new_data.pth'
)
```

## 便捷函数

### 1. `patent_continue_training` - 继续训练
```python
# 简化的继续训练接口
model, scaler = patent_continue_training(
    kline_data_list=data,
    pretrained_model_path='patent_model.pth',
    additional_epochs_pretrain=5,
    additional_epochs_finetune=3,
    inherit_epochs=True,
    model_path='patent_model_continued.pth'
)
```

### 2. `patent_train_with_new_data` - 新数据训练
```python
# 专门用于新数据的训练接口
model, scaler = patent_train_with_new_data(
    kline_data_list=new_data,
    pretrained_model_path='patent_model.pth',
    epochs_pretrain=10,
    epochs_finetune=5,
    model_path='patent_model_new_data.pth'
)
```

## 轮数继承逻辑

### 继承轮数 (`inherit_epochs=True`)
- 从原模型的轮数继续计数
- 适用于相同数据的继续训练
- 例：原模型训练了20轮，继续训练10轮，最终显示30轮

### 不继承轮数 (`inherit_epochs=False`)
- 从指定的起始轮数开始计数
- 适用于新数据或重新开始的训练
- 例：无论原模型训练了多少轮，新训练从第0轮开始

## 数据标准化器处理

1. **自动加载**：如果预训练模型包含标准化器，会自动加载
2. **兼容性检查**：检查标准化器是否与当前数据兼容
3. **自动回退**：如果不兼容，会重新训练标准化器

## 模型信息保存

保存的模型现在包含：
- `model_state_dict`: 模型权重
- `scaler`: 数据标准化器
- `k`: 序列长度
- `model_config`: 模型配置
- `epochs_info`: 训练轮数信息
- `training_params`: 训练参数

## 实际应用建议

### 1. 模型优化场景
```python
# 第一次训练
model1, scaler1 = patent_train_model(data, epochs_pretrain=50, epochs_finetune=20)

# 发现需要更多训练，继续训练
model2, scaler2 = patent_continue_training(
    data, 'patent_model.pth', 
    additional_epochs_pretrain=20, 
    additional_epochs_finetune=10,
    inherit_epochs=True
)
```

### 2. 新数据适应场景
```python
# 原始模型
model1, scaler1 = patent_train_model(old_data, epochs_pretrain=100, epochs_finetune=30)

# 新数据训练（不继承轮数）
model2, scaler2 = patent_train_with_new_data(
    new_data, 'patent_model.pth',
    epochs_pretrain=50, epochs_finetune=15
)
```

## 注意事项

1. **数据兼容性**：使用不同数据时，建议不继承轮数
2. **模型配置**：预训练模型的配置会被自动加载
3. **早停机制**：继续训练时早停机制仍然有效
4. **文件管理**：建议为不同阶段的模型使用不同的文件名

## 测试验证

运行 `test_continue_training.py` 可以验证所有功能是否正常工作。
