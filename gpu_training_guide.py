#!/usr/bin/env python3
"""
GPU训练指南和示例
专利CN113159945A股票预测模型GPU加速训练
"""

import torch
from patent_stock_predictor import patent_train_model, patent_predict, generate_sample_data, get_recommended_early_stopping_params

def check_gpu_setup():
    """检查GPU设置"""
    print("🔍 GPU环境检查")
    print("-" * 40)
    
    if torch.cuda.is_available():
        device_name = torch.cuda.get_device_name(0)
        memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"✅ GPU可用: {device_name}")
        print(f"✅ 显存: {memory_gb:.1f} GB")
        
        # 推荐batch size
        if memory_gb >= 12:
            pretrain_bs, lstm_bs = 32, 64
        elif memory_gb >= 8:
            pretrain_bs, lstm_bs = 24, 48
        else:
            pretrain_bs, lstm_bs = 16, 32
            
        print(f"📊 推荐batch size: 预训练={pretrain_bs}, LSTM={lstm_bs}")
        return True
    else:
        print("❌ GPU不可用，将使用CPU训练")
        print("💡 安装GPU版PyTorch: pip install torch --index-url https://download.pytorch.org/whl/cu118")
        return False

def gpu_training_example():
    """GPU训练示例"""
    print("\n🚀 开始GPU训练示例")
    print("=" * 50)
    
    # 检查GPU
    gpu_available = check_gpu_setup()
    
    # 生成示例数据
    print("\n📊 准备训练数据...")
    num_stocks = 5
    days_per_stock = 100
    sample_data = generate_sample_data(num_stocks=num_stocks, days_per_stock=days_per_stock)
    print(f"生成了{len(sample_data)}只股票的数据，每只股票{len(sample_data[0])}天")
    
    # 获取推荐参数
    params = get_recommended_early_stopping_params(num_stocks, days_per_stock)
    print(f"推荐训练参数: {params}")
    
    # 开始训练
    print(f"\n🎯 开始训练（{'GPU' if gpu_available else 'CPU'}模式）...")
    
    try:
        model, scaler = patent_train_model(
            kline_data_list=sample_data,
            k=5,
            epochs_pretrain=params['epochs_pretrain'],
            epochs_finetune=params['epochs_finetune'],
            model_path='gpu_patent_model.pth',
            early_stopping=True,
            patience=params['patience'],
            min_delta=params['min_delta']
        )
        
        print("✅ 训练完成！")
        
        # 测试预测
        print("\n🔮 测试预测...")
        test_data = sample_data[0][-5:]
        result = patent_predict(test_data, model_path='gpu_patent_model.pth')
        print(f"预测结果: {result}")
        
        # GPU显存使用情况
        if gpu_available:
            allocated = torch.cuda.memory_allocated() / 1024**3
            reserved = torch.cuda.memory_reserved() / 1024**3
            print(f"\n💾 显存使用: 已分配={allocated:.2f}GB, 缓存={reserved:.2f}GB")
            
            # 清理显存
            torch.cuda.empty_cache()
            print("🧹 显存已清理")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

def gpu_optimization_tips():
    """GPU优化建议"""
    print("\n💡 GPU训练优化建议")
    print("=" * 30)
    
    tips = [
        "1. 使用混合精度训练可以节省显存：torch.cuda.amp",
        "2. 适当增大batch size可以提高GPU利用率",
        "3. 使用torch.compile()可以加速训练（PyTorch 2.0+）",
        "4. 定期清理显存：torch.cuda.empty_cache()",
        "5. 监控显存使用：torch.cuda.memory_allocated()",
        "6. 使用DataLoader的pin_memory=True加速数据传输",
        "7. 设置CUDA_LAUNCH_BLOCKING=1调试CUDA错误"
    ]
    
    for tip in tips:
        print(f"  {tip}")

def benchmark_cpu_vs_gpu():
    """CPU vs GPU性能对比"""
    print("\n⚡ 性能对比测试")
    print("-" * 20)
    
    if not torch.cuda.is_available():
        print("❌ 需要GPU才能进行对比测试")
        return
    
    import time
    
    # 创建测试数据
    batch_size = 32
    seq_len = 5
    input_dim = 6
    
    # CPU测试
    print("🖥️  CPU测试...")
    data_cpu = torch.randn(batch_size, seq_len, input_dim)
    start_time = time.time()
    
    # 模拟一些计算
    for _ in range(100):
        result = torch.mm(data_cpu.view(-1, input_dim), torch.randn(input_dim, 128))
    
    cpu_time = time.time() - start_time
    print(f"CPU时间: {cpu_time:.3f}秒")
    
    # GPU测试
    print("🚀 GPU测试...")
    data_gpu = torch.randn(batch_size, seq_len, input_dim).cuda()
    torch.cuda.synchronize()  # 确保GPU操作完成
    start_time = time.time()
    
    for _ in range(100):
        result = torch.mm(data_gpu.view(-1, input_dim), torch.randn(input_dim, 128).cuda())
    
    torch.cuda.synchronize()
    gpu_time = time.time() - start_time
    print(f"GPU时间: {gpu_time:.3f}秒")
    
    speedup = cpu_time / gpu_time
    print(f"🏃 加速比: {speedup:.1f}x")

if __name__ == "__main__":
    print("🎯 专利CN113159945A GPU训练指南")
    print("=" * 50)
    
    # 运行示例
    gpu_training_example()
    
    # 显示优化建议
    gpu_optimization_tips()
    
    # 性能对比（如果有GPU）
    if torch.cuda.is_available():
        benchmark_cpu_vs_gpu()
    
    print("\n🎉 GPU训练指南完成！")
