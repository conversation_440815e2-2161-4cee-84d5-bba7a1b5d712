#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于本地K线文件的回测系统
实现类似赌大小的交易策略：
- 如果买多：判断次日开盘价大于当日开盘价则赢
- 如果买空：判断次日开盘价小于当日开盘价则赢
- 赔率为1:0.8
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime
from sklearn.ensemble import RandomForestClassifier
from typing import List, Tuple, Dict
import warnings
warnings.filterwarnings('ignore')

class BacktestSystem:
    def __init__(self, kline_file: str = "BTCUSDT_10m.json", win_ratio: float = 0.8):
        """
        初始化回测系统
        
        Args:
            kline_file: K线数据文件路径
            win_ratio: 赢的赔率（输的话损失本金，赢的话获得本金*win_ratio的收益）
        """
        self.kline_file = kline_file
        self.win_ratio = win_ratio
        self.klines_df = None
        self.predictions = []
        self.trades = []
        self.initial_balance = 10000  # 初始资金
        self.current_balance = self.initial_balance
        
    def load_klines(self) -> bool:
        """加载K线数据"""
        try:
            with open(self.kline_file, 'r') as f:
                klines_data = json.load(f)
            
            # 转换为DataFrame
            columns = ['open_time', 'open', 'high', 'low', 'close', 'volume', 
                      'close_time', 'quote_volume', 'count', 'taker_buy_volume', 
                      'taker_buy_quote_volume', 'ignore']
            
            self.klines_df = pd.DataFrame(klines_data, columns=columns)
            
            # 转换数据类型
            self.klines_df['open_time'] = pd.to_datetime(self.klines_df['open_time'], unit='ms')
            self.klines_df['close_time'] = pd.to_datetime(self.klines_df['close_time'], unit='ms')
            self.klines_df['open'] = self.klines_df['open'].astype(float)
            self.klines_df['high'] = self.klines_df['high'].astype(float)
            self.klines_df['low'] = self.klines_df['low'].astype(float)
            self.klines_df['close'] = self.klines_df['close'].astype(float)
            
            # 添加技术指标
            self.calculate_indicators()
            
            print(f"成功加载 {len(self.klines_df)} 条K线数据")
            print(f"数据时间范围: {self.klines_df['open_time'].iloc[0]} 到 {self.klines_df['open_time'].iloc[-1]}")
            return True
            
        except Exception as e:
            print(f"加载K线数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标"""
        # 简单移动平均线
        self.klines_df['sma_5'] = self.klines_df['close'].rolling(window=5).mean()
        self.klines_df['sma_10'] = self.klines_df['close'].rolling(window=10).mean()
        self.klines_df['sma_20'] = self.klines_df['close'].rolling(window=20).mean()
        
        # 指数移动平均线
        self.klines_df['ema_5'] = self.klines_df['close'].ewm(span=5).mean()
        self.klines_df['ema_10'] = self.klines_df['close'].ewm(span=10).mean()
        
        # RSI指标
        self.klines_df['rsi'] = self.calculate_rsi(self.klines_df['close'], 14)
        
        # 价格变化率
        self.klines_df['price_change'] = self.klines_df['close'].pct_change()
        self.klines_df['price_change_5'] = self.klines_df['close'].pct_change(5)
        
        # 波动率
        self.klines_df['volatility'] = self.klines_df['price_change'].rolling(window=10).std()
        
        # 下一个开盘价（用于判断输赢）
        self.klines_df['next_open'] = self.klines_df['open'].shift(-1)
        
        # 涨跌标签（当前开盘价 vs 下一个开盘价）
        self.klines_df['next_up'] = (self.klines_df['next_open'] > self.klines_df['open']).astype(int)
    
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def prepare_features(self, lookback_period: int = 50) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备机器学习特征
        
        Args:
            lookback_period: 用于训练的历史数据长度
            
        Returns:
            特征矩阵和标签向量
        """
        # 选择特征列
        feature_columns = ['sma_5', 'sma_10', 'sma_20', 'ema_5', 'ema_10', 
                          'rsi', 'price_change', 'price_change_5', 'volatility']
        
        # 去除包含NaN的行
        df_clean = self.klines_df.dropna()
        
        if len(df_clean) < lookback_period + 1:
            raise ValueError(f"数据不足，需要至少 {lookback_period + 1} 条有效数据")
        
        # 准备特征和标签
        features = df_clean[feature_columns].values
        labels = df_clean['next_up'].values
        
        return features, labels
    
    def run_backtest(self, train_period: int = 100, prediction_period: int = 1, sample_rate: int = 10):
        """
        运行回测

        Args:
            train_period: 训练数据的长度
            prediction_period: 预测周期（暂时固定为1，即预测下一个周期）
            sample_rate: 采样率，每隔多少个数据点进行一次预测（减少计算量）
        """
        if self.klines_df is None:
            print("请先加载K线数据")
            return

        features, labels = self.prepare_features()

        # 确保有足够的数据进行回测
        if len(features) < train_period + 50:
            print(f"数据不足，需要至少 {train_period + 50} 条有效数据")
            return

        # 计算实际要处理的数据点数量
        total_points = len(features) - train_period - 1
        sample_points = list(range(train_period, len(features) - 1, sample_rate))

        print(f"开始回测，训练周期: {train_period}, 总数据量: {len(features)}")
        print(f"采样率: {sample_rate}, 实际预测次数: {len(sample_points)}")

        # 从训练周期结束后开始预测和交易
        for idx, i in enumerate(sample_points):
            # 显示进度
            if idx % 100 == 0:
                progress = idx / len(sample_points) * 100
                print(f"进度: {progress:.1f}% ({idx}/{len(sample_points)})")

            # 准备训练数据
            X_train = features[i-train_period:i]
            y_train = labels[i-train_period:i]

            # 当前数据点用于预测
            X_predict = features[i:i+1]

            # 训练随机森林模型（减少树的数量以提高速度）
            clf = RandomForestClassifier(n_estimators=20, random_state=42, n_jobs=-1)
            clf.fit(X_train, y_train)

            # 预测
            prediction = clf.predict(X_predict)[0]
            prediction_proba = clf.predict_proba(X_predict)[0]
            
            # 获取当前和下一个开盘价
            current_open = self.klines_df.iloc[i]['open']
            next_open = self.klines_df.iloc[i+1]['open']
            current_time = self.klines_df.iloc[i]['open_time']
            
            # 实际涨跌情况
            actual_up = 1 if next_open > current_open else 0
            
            # 记录预测结果
            self.predictions.append({
                'time': current_time,
                'prediction': prediction,
                'actual': actual_up,
                'current_open': current_open,
                'next_open': next_open,
                'confidence': max(prediction_proba)
            })
            
            # 执行交易（固定金额策略）
            trade_amount = 5  # 固定每次交易5元

            if self.current_balance >= trade_amount:
                # 判断输赢
                win = (prediction == actual_up)

                if win:
                    profit = trade_amount * self.win_ratio
                    self.current_balance += profit
                    result = "WIN"
                else:
                    loss = trade_amount
                    self.current_balance -= loss
                    result = "LOSS"

                # 记录交易
                self.trades.append({
                    'time': current_time,
                    'prediction': "买多" if prediction == 1 else "买空",
                    'trade_amount': trade_amount,
                    'result': result,
                    'profit_loss': profit if win else -loss,
                    'balance': self.current_balance,
                    'current_open': current_open,
                    'next_open': next_open
                })
            else:
                # 资金不足，停止交易
                print(f"资金不足，停止交易。当前余额: {self.current_balance:.2f}")
                break
        
        self.analyze_results()
    
    def analyze_results(self):
        """分析回测结果"""
        if not self.predictions:
            print("没有预测结果")
            return
        
        # 转换为DataFrame便于分析
        pred_df = pd.DataFrame(self.predictions)
        trade_df = pd.DataFrame(self.trades)
        
        # 计算准确率
        accuracy = (pred_df['prediction'] == pred_df['actual']).mean()
        
        # 计算收益统计
        total_trades = len(trade_df)
        win_trades = len(trade_df[trade_df['result'] == 'WIN'])
        win_rate = win_trades / total_trades if total_trades > 0 else 0
        
        total_profit = trade_df['profit_loss'].sum()
        total_return = (self.current_balance - self.initial_balance) / self.initial_balance
        
        print("\n" + "="*50)
        print("回测结果分析")
        print("="*50)
        print(f"预测准确率: {accuracy:.2%}")
        print(f"总交易次数: {total_trades}")
        print(f"获胜交易次数: {win_trades}")
        print(f"交易胜率: {win_rate:.2%}")
        print(f"初始资金: {self.initial_balance:,.2f}")
        print(f"最终资金: {self.current_balance:,.2f}")
        print(f"总收益: {total_profit:,.2f}")
        print(f"总收益率: {total_return:.2%}")
        
        # 显示最近几次交易
        print(f"\n最近10次交易:")
        print(trade_df[['time', 'prediction', 'result', 'profit_loss', 'balance']].tail(10).to_string(index=False))
        
        # 计算最大回撤
        balance_series = trade_df['balance']
        running_max = balance_series.expanding().max()
        drawdown = (balance_series - running_max) / running_max
        max_drawdown = drawdown.min()
        
        print(f"\n最大回撤: {max_drawdown:.2%}")
        
        return {
            'accuracy': accuracy,
            'win_rate': win_rate,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades
        }

def main():
    """主函数"""
    # 创建回测系统
    backtest = BacktestSystem(kline_file="BTCUSDT_10m.json", win_ratio=0.8)

    # 加载数据
    if not backtest.load_klines():
        return

    # 运行回测（使用采样率减少计算量）
    backtest.run_backtest(train_period=100, sample_rate=20)

if __name__ == "__main__":
    main()
