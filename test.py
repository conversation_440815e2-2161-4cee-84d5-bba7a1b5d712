import requests
import json
import os
from datetime import datetime, timedelta
from typing import List, Tuple
import time

def get_klines(symbol: str, interval: str, start_time: int = None, end_time: int = None, limit: int = 1500) -> List:
    try:
        """获取价格指数K线数据"""
        url = "https://www.binance.com/fapi/v1/klines"
        params = {
            "symbol": symbol,
            "interval": interval,
            "limit": limit
        }
        
        if start_time:
            params["startTime"] = start_time
        if end_time:
            params["endTime"] = end_time
            
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error: {e}")
        return []

def date_to_timestamp(date_str: str) -> int:
    """将日期字符串转换为时间戳（毫秒）"""
    dt = datetime.strptime(date_str, "%Y-%m-%d")
    return int(dt.timestamp() * 1000)

def get_klines_by_date_range(symbol: str, interval: str, start_date: str, end_date: str, 
                           contract_type: str = "PERPETUAL") -> List:
    """根据日期范围获取K线数据"""
    start_ts = date_to_timestamp(start_date)
    end_ts = date_to_timestamp(end_date) + 24 * 60 * 60 * 1000 - 1  # 结束日期的23:59:59
    
    all_data = []
    current_start = start_ts
    
    while current_start < end_ts:
        print(f"获取数据: {datetime.fromtimestamp(current_start/1000)}")
        
        data = get_klines(symbol, interval, current_start, end_ts, 1500)
        
        if not data:
            break
            
        all_data.extend(data)
        
        # 更新下次请求的开始时间
        last_close_time = int(data[-1][6])  # 收盘时间
        current_start = last_close_time + 1
        
        if len(data) < 1500:
            break
        # 避免请求过于频繁
        time.sleep(0.1)
    
    return all_data

def load_local_data(filename: str) -> List:
    """加载本地数据"""
    if os.path.exists(filename):
        with open(filename, 'r') as f:
            return json.load(f)
    return []

def save_data(data: List, filename: str):
    """保存数据到本地文件"""
    with open(filename, 'w') as f:
        json.dump(data, f, indent=2)

def merge_klines_data(symbol: str, interval: str, start_date: str, end_date: str, 
                     contract_type: str = "PERPETUAL"):
    """合并K线数据并持久化保存"""
    filename = f"{symbol}_{interval}.json"
    
    # 加载本地数据
    local_data = load_local_data(filename)
    local_data = local_data[:-1]
    # 确定需要获取的时间范围
    start_ts = date_to_timestamp(start_date)
    end_ts = date_to_timestamp(end_date) + 24 * 60 * 60 * 1000 - 1
    
    if local_data:
        # 本地数据的时间范围（使用开盘时间）
        local_start = int(local_data[0][0])
        local_end = int(local_data[-1][0])
        
        print(f"本地数据范围: {datetime.fromtimestamp(local_start/1000)} - {datetime.fromtimestamp(local_end/1000)}")
        
        # 需要获取的新数据范围
        fetch_ranges = []
        
        if start_ts < local_start:
            fetch_ranges.append((start_ts, local_start - 1))
            
        if end_ts > local_end:
            fetch_ranges.append((local_end + 1, end_ts))
    else:
        fetch_ranges = [(start_ts, end_ts)]
    
    # 获取新数据
    new_data = []
    for fetch_start, fetch_end in fetch_ranges:
        start_date_str = datetime.fromtimestamp(fetch_start/1000).strftime("%Y-%m-%d")
        end_date_str = datetime.fromtimestamp(fetch_end/1000).strftime("%Y-%m-%d")
        
        print(f"获取新数据范围: {start_date_str} - {end_date_str}")
        range_data = get_klines_by_date_range(symbol, interval, start_date_str, end_date_str, contract_type)
        new_data.extend(range_data)
    
    # 合并数据
    all_data = local_data + new_data
    
    # 去重并排序（按开盘时间）
    unique_data = {}
    for item in all_data:
        open_time = int(item[0])
        unique_data[open_time] = item
    
    final_data = sorted(unique_data.values(), key=lambda x: int(x[0]))
    
    # 保存数据
    save_data(final_data, filename)
    print(f"数据已保存到 {filename}，共 {len(final_data)} 条记录")
    
    return final_data

def convert_1m_to_10m_klines(input_filename: str = "BTCUSDT_1m.json",
                            output_filename: str = "BTCUSDT_10m.json") -> List:
    """
    读取1分钟K线数据并合成10分钟K线数据

    Args:
        input_filename: 1分钟K线数据文件名
        output_filename: 输出的10分钟K线数据文件名

    Returns:
        合成的10分钟K线数据列表
    """
    # 读取1分钟K线数据
    try:
        with open(input_filename, 'r') as f:
            data_1m = json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到文件 {input_filename}")
        return []
    except json.JSONDecodeError:
        print(f"错误：文件 {input_filename} 格式不正确")
        return []

    if not data_1m:
        print("错误：1分钟K线数据为空")
        return []

    print(f"读取到 {len(data_1m)} 条1分钟K线数据")

    # 合成10分钟K线数据
    data_10m = []

    # 按10分钟时间窗口分组
    i = 0
    while i < len(data_1m):
        # 获取当前1分钟K线的开盘时间
        current_open_time = int(data_1m[i][0])

        # 计算10分钟窗口的开始时间（向下取整到10分钟边界）
        # 例如：13:07 -> 13:00, 13:15 -> 13:10
        minutes = (current_open_time // 60000) % 60  # 获取分钟数
        window_start_minutes = (minutes // 10) * 10  # 向下取整到10分钟边界
        window_start_time = current_open_time - (minutes - window_start_minutes) * 60000

        # 10分钟窗口的结束时间
        window_end_time = window_start_time + 10 * 60 * 1000 - 1

        # 收集这个10分钟窗口内的所有1分钟K线
        window_klines = []
        j = i
        while j < len(data_1m):
            kline_open_time = int(data_1m[j][0])
            if kline_open_time >= window_start_time and kline_open_time < window_start_time + 10 * 60 * 1000:
                window_klines.append(data_1m[j])
                j += 1
            else:
                break

        if len(window_klines) < 10:
            break
        # 合成10分钟K线
        # 开盘价：第一根1分钟K线的开盘价
        open_price = window_klines[0][1]

        # 收盘价：最后一根1分钟K线的收盘价
        close_price = window_klines[-1][4]

        # 最高价：所有1分钟K线中的最高价
        high_price = max(float(kline[2]) for kline in window_klines)

        # 最低价：所有1分钟K线中的最低价
        low_price = min(float(kline[3]) for kline in window_klines)

        # 成交量
        volume = sum(float(kline[5]) for kline in window_klines)
        # 成交额
        turnover = sum(float(kline[7]) for kline in window_klines)
        # 成交笔数
        count = sum(int(kline[8]) for kline in window_klines)
        # 主动买入成交量
        taker_buy_volume = sum(float(kline[9]) for kline in window_klines)
        # 主动买入成交额
        taker_buy_quote_volume = sum(float(kline[10]) for kline in window_klines)

        # 构造10分钟K线数据
        kline_10m = [
            window_start_time,             # 开盘时间
            open_price,                    # 开盘价
            str(high_price),               # 最高价
            str(low_price),                # 最低价
            close_price,                   # 收盘价
            str(volume),                   # 成交量忽略
            window_end_time,               # 收盘时间
            str(turnover),                 # 成交额忽略
            str(count),                    # 成交笔数忽略
            str(taker_buy_volume),         # 主动买入成交量忽略
            str(taker_buy_quote_volume),   # 主动买入成交额忽略
            "0"                            # 忽略
        ]

        data_10m.append(kline_10m)
        print(f"合成10分钟K线: {datetime.fromtimestamp(window_start_time/1000)} - {datetime.fromtimestamp(window_end_time/1000)}, 包含{len(window_klines)}根1分钟K线")

        # 移动到下一个窗口
        i = j if j > i else i + 1

    # 保存10分钟K线数据
    try:
        with open(output_filename, 'w') as f:
            json.dump(data_10m, f, indent=2)
        print(f"10分钟K线数据已保存到 {output_filename}，共 {len(data_10m)} 条记录")
    except Exception as e:
        print(f"保存文件时出错：{e}")

    return data_10m

# 使用示例
if __name__ == "__main__":
    # 获取BTCUSDT 1分钟K线数据
    symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "SOLUSDT", "ADAUSDT", "XRPUSDT", "DOGEUSDT", "AVAXUSDT", "LINKUSDT", "LTCUSDT"]
    for s in symbols:
        data = merge_klines_data(s, "30m", "2024-01-01", "2025-08-31")

    # 将1分钟K线数据转换为10分钟K线数据
    # data_10m = convert_1m_to_10m_klines("BTCUSDT_1m.json", "BTCUSDT_10m.json")
