#!/usr/bin/env python3
"""
GPU环境检查脚本
"""

import torch
import sys

def check_gpu_environment():
    """检查GPU环境配置"""
    print("=" * 60)
    print("🔍 GPU环境检查")
    print("=" * 60)
    
    # PyTorch版本
    print(f"PyTorch版本: {torch.__version__}")
    
    # CUDA支持
    cuda_available = torch.cuda.is_available()
    print(f"CUDA是否可用: {cuda_available}")
    
    if cuda_available:
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"cuDNN版本: {torch.backends.cudnn.version()}")
        
        # GPU信息
        gpu_count = torch.cuda.device_count()
        print(f"GPU数量: {gpu_count}")
        
        for i in range(gpu_count):
            props = torch.cuda.get_device_properties(i)
            print(f"\nGPU {i}: {props.name}")
            print(f"  显存: {props.total_memory / 1024**3:.1f} GB")
            print(f"  计算能力: {props.major}.{props.minor}")
            print(f"  多处理器数量: {props.multi_processor_count}")
        
        # 当前设备
        current_device = torch.cuda.current_device()
        print(f"\n当前GPU设备: {current_device}")
        
        # 显存使用情况
        print(f"已分配显存: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
        print(f"缓存显存: {torch.cuda.memory_reserved() / 1024**3:.2f} GB")
        
        # 简单测试
        print("\n🧪 GPU功能测试...")
        try:
            x = torch.randn(1000, 1000).cuda()
            y = torch.randn(1000, 1000).cuda()
            z = torch.mm(x, y)
            print("✅ GPU矩阵运算测试通过")
            
            # 清理显存
            del x, y, z
            torch.cuda.empty_cache()
            
        except Exception as e:
            print(f"❌ GPU测试失败: {e}")
            
    else:
        print("\n💡 如何启用GPU:")
        print("1. 确保安装了NVIDIA GPU驱动")
        print("2. 安装CUDA Toolkit")
        print("3. 安装GPU版本的PyTorch:")
        print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
    
    print("\n" + "=" * 60)
    return cuda_available

def get_recommended_batch_size():
    """根据GPU显存推荐batch size"""
    if not torch.cuda.is_available():
        return {"pretrain": 16, "lstm": 32}
    
    # 获取GPU显存
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    
    if gpu_memory >= 24:  # 24GB+
        return {"pretrain": 64, "lstm": 128}
    elif gpu_memory >= 12:  # 12GB+
        return {"pretrain": 32, "lstm": 64}
    elif gpu_memory >= 8:   # 8GB+
        return {"pretrain": 24, "lstm": 48}
    elif gpu_memory >= 6:   # 6GB+
        return {"pretrain": 16, "lstm": 32}
    else:  # <6GB
        return {"pretrain": 8, "lstm": 16}

if __name__ == "__main__":
    cuda_available = check_gpu_environment()
    
    if cuda_available:
        batch_sizes = get_recommended_batch_size()
        print(f"🎯 推荐batch size:")
        print(f"  自监督预训练: {batch_sizes['pretrain']}")
        print(f"  LSTM训练: {batch_sizes['lstm']}")
