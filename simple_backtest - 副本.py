#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版回测系统 - 固定赔率0.8和交易金额5，测试几个关键参数
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime
from sklearn.ensemble import RandomForestClassifier
from typing import List, Tuple, Dict
import warnings
warnings.filterwarnings('ignore')

class SimpleBacktestSystem:
    def __init__(self, kline_file: str = "BTCUSDT_10m.json"):
        """初始化简化版回测系统"""
        self.kline_file = kline_file
        self.klines_df = None
        self.win_ratio = 0.8  # 固定赔率
        self.trade_amount = 5  # 固定交易金额
        
    def load_klines(self) -> bool:
        """加载K线数据"""
        try:
            with open(self.kline_file, 'r') as f:
                klines_data = json.load(f)
            
            # 转换为DataFrame
            columns = ['open_time', 'open', 'high', 'low', 'close', 'volume', 
                      'close_time', 'quote_volume', 'count', 'taker_buy_volume', 
                      'taker_buy_quote_volume', 'ignore']
            
            self.klines_df = pd.DataFrame(klines_data, columns=columns)
            
            # 转换数据类型
            self.klines_df['open_time'] = pd.to_datetime(self.klines_df['open_time'], unit='ms')
            self.klines_df['close_time'] = pd.to_datetime(self.klines_df['close_time'], unit='ms')
            self.klines_df['open'] = self.klines_df['open'].astype(float)
            self.klines_df['high'] = self.klines_df['high'].astype(float)
            self.klines_df['low'] = self.klines_df['low'].astype(float)
            self.klines_df['close'] = self.klines_df['close'].astype(float)
            
            # 计算技术指标
            self.calculate_indicators()
            
            print(f"成功加载 {len(self.klines_df)} 条K线数据")
            print(f"数据时间范围: {self.klines_df['open_time'].iloc[0]} 到 {self.klines_df['open_time'].iloc[-1]}")
            return True
            
        except Exception as e:
            print(f"加载K线数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标"""
        # 简单移动平均线
        self.klines_df['sma_5'] = self.klines_df['close'].rolling(window=5).mean()
        self.klines_df['sma_10'] = self.klines_df['close'].rolling(window=10).mean()
        self.klines_df['sma_20'] = self.klines_df['close'].rolling(window=20).mean()
        
        # 指数移动平均线
        self.klines_df['ema_5'] = self.klines_df['close'].ewm(span=5).mean()
        self.klines_df['ema_10'] = self.klines_df['close'].ewm(span=10).mean()
        
        # RSI指标
        self.klines_df['rsi'] = self.calculate_rsi(self.klines_df['close'], 14)
        
        # 价格变化率
        self.klines_df['price_change'] = self.klines_df['close'].pct_change()
        self.klines_df['price_change_5'] = self.klines_df['close'].pct_change(5)
        
        # 波动率
        self.klines_df['volatility'] = self.klines_df['price_change'].rolling(window=10).std()
        
        # 下一个开盘价（用于判断输赢）
        self.klines_df['next_open'] = self.klines_df['open'].shift(-1)
        
        # 涨跌标签（当前开盘价 vs 下一个开盘价）
        self.klines_df['next_up'] = (self.klines_df['next_open'] > self.klines_df['open']).astype(int)
    
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def prepare_features(self) -> Tuple[np.ndarray, np.ndarray, pd.DataFrame]:
        """准备机器学习特征"""
        feature_columns = ['sma_5', 'sma_10', 'sma_20', 'ema_5', 'ema_10', 
                          'rsi', 'price_change', 'price_change_5', 'volatility']
        
        # 去除包含NaN的行
        df_clean = self.klines_df.dropna()
        
        # 准备特征和标签
        features = df_clean[feature_columns].values
        labels = df_clean['next_up'].values
        
        return features, labels, df_clean
    
    def run_backtest(self, train_period: int = 100, sample_rate: int = 20,
                    n_estimators: int = 20, initial_balance: float = 1000, 
                    verbose: bool = True) -> Dict:
        """运行回测"""
        
        features, labels, df_clean = self.prepare_features()
        
        if len(features) < train_period + 50:
            if verbose:
                print(f"数据不足，需要至少 {train_period + 50} 条有效数据")
            return None
        
        # 初始化变量
        current_balance = initial_balance
        trades = []
        predictions = []
        
        sample_points = list(range(train_period, len(features) - 1, sample_rate))
        
        if verbose:
            print(f"开始回测，训练周期: {train_period}, 采样率: {sample_rate}")
            print(f"树的数量: {n_estimators}, 实际预测次数: {len(sample_points)}")
        
        for idx, i in enumerate(sample_points):
            # 显示进度
            if verbose and idx % 200 == 0:
                progress = idx / len(sample_points) * 100
                print(f"进度: {progress:.1f}% ({idx}/{len(sample_points)})")
            
            # 准备训练数据
            X_train = features[i-train_period:i]
            y_train = labels[i-train_period:i]
            X_predict = features[i:i+1]
            
            # 训练模型（不使用并行处理避免问题）
            clf = RandomForestClassifier(n_estimators=n_estimators, random_state=42, n_jobs=1)
            clf.fit(X_train, y_train)
            
            # 预测
            prediction = clf.predict(X_predict)[0]
            
            # 获取价格信息
            current_open = df_clean.iloc[i]['open']
            next_open = df_clean.iloc[i+1]['open']
            current_time = df_clean.iloc[i]['open_time']
            actual_up = 1 if next_open > current_open else 0
            
            # 记录预测
            predictions.append({
                'time': current_time,
                'prediction': prediction,
                'actual': actual_up,
                'current_open': current_open,
                'next_open': next_open
            })
            
            # 执行交易
            if current_balance >= self.trade_amount:
                win = (prediction == actual_up)
                
                if win:
                    profit = self.trade_amount * self.win_ratio
                    current_balance += profit
                    result = "WIN"
                else:
                    loss = self.trade_amount
                    current_balance -= loss
                    result = "LOSS"
                
                trades.append({
                    'time': current_time,
                    'prediction': "买多" if prediction == 1 else "买空",
                    'trade_amount': self.trade_amount,
                    'result': result,
                    'profit_loss': profit if win else -loss,
                    'balance': current_balance,
                    'current_open': current_open,
                    'next_open': next_open
                })
            else:
                if verbose:
                    print(f"资金不足，停止交易。当前余额: {current_balance:.2f}")
                break
        
        # 计算结果
        if not trades:
            return None
            
        pred_df = pd.DataFrame(predictions[:len(trades)])
        trade_df = pd.DataFrame(trades)
        
        accuracy = (pred_df['prediction'] == pred_df['actual']).mean()
        win_rate = len(trade_df[trade_df['result'] == 'WIN']) / len(trade_df)
        total_return = (current_balance - initial_balance) / initial_balance
        
        # 计算最大回撤
        balance_series = trade_df['balance']
        running_max = balance_series.expanding().max()
        drawdown = (balance_series - running_max) / running_max
        max_drawdown = drawdown.min()
        
        result = {
            'train_period': train_period,
            'sample_rate': sample_rate,
            'n_estimators': n_estimators,
            'accuracy': accuracy,
            'win_rate': win_rate,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'total_trades': len(trades),
            'final_balance': current_balance,
            'trades': trades
        }
        
        if verbose:
            self.print_results(result)
        
        return result
    
    def print_results(self, result: Dict):
        """打印回测结果"""
        print("\n" + "="*50)
        print("回测结果分析")
        print("="*50)
        print(f"训练周期: {result['train_period']}")
        print(f"采样率: {result['sample_rate']}")
        print(f"随机森林树数量: {result['n_estimators']}")
        print(f"预测准确率: {result['accuracy']:.2%}")
        print(f"总交易次数: {result['total_trades']}")
        print(f"交易胜率: {result['win_rate']:.2%}")
        print(f"总收益率: {result['total_return']:.2%}")
        print(f"最大回撤: {result['max_drawdown']:.2%}")
        print(f"最终余额: {result['final_balance']:.2f}")
        
        # 显示最近几次交易
        if result['trades']:
            trade_df = pd.DataFrame(result['trades'])
            print(f"\n最近10次交易:")
            print(trade_df[['time', 'prediction', 'result', 'profit_loss', 'balance']].tail(10).to_string(index=False))
    
    def test_key_parameters(self):
        """测试几个关键参数组合"""
        print("测试关键参数组合...")
        print(f"固定参数: 赔率={self.win_ratio}, 交易金额={self.trade_amount}")
        
        # 测试参数组合
        test_cases = [
            {'train_period': 50, 'sample_rate': 10, 'n_estimators': 10},
            {'train_period': 100, 'sample_rate': 20, 'n_estimators': 20},
            {'train_period': 150, 'sample_rate': 30, 'n_estimators': 30},
            {'train_period': 200, 'sample_rate': 50, 'n_estimators': 20},
        ]
        
        results = []
        
        for i, params in enumerate(test_cases):
            print(f"\n测试案例 {i+1}/{len(test_cases)}")
            print(f"参数: {params}")
            
            result = self.run_backtest(
                train_period=params['train_period'],
                sample_rate=params['sample_rate'],
                n_estimators=params['n_estimators'],
                initial_balance=1000,
                verbose=True
            )
            
            if result:
                results.append(result)
        
        # 比较结果
        if results:
            print("\n" + "="*80)
            print("参数测试结果比较")
            print("="*80)
            
            results_df = pd.DataFrame(results)
            results_df_sorted = results_df.sort_values('total_return', ascending=False)
            
            print("按总收益率排序:")
            print(results_df_sorted[['train_period', 'sample_rate', 'n_estimators',
                                    'accuracy', 'win_rate', 'total_return', 'max_drawdown', 
                                    'total_trades']].to_string(index=False))
            
            # 找出最佳参数
            best_result = results_df_sorted.iloc[0]
            print(f"\n最佳参数组合:")
            print(f"训练周期: {best_result['train_period']}")
            print(f"采样率: {best_result['sample_rate']}")
            print(f"树数量: {best_result['n_estimators']}")
            print(f"预测准确率: {best_result['accuracy']:.2%}")
            print(f"交易胜率: {best_result['win_rate']:.2%}")
            print(f"总收益率: {best_result['total_return']:.2%}")
            print(f"最大回撤: {best_result['max_drawdown']:.2%}")
            
            return results_df_sorted
        else:
            print("没有有效的回测结果")
            return None

def main():
    """主函数"""
    # 创建简化版回测系统
    backtest = SimpleBacktestSystem(kline_file="BTCUSDT_10m.json")
    
    # 加载数据
    if not backtest.load_klines():
        return
    
    # 测试关键参数
    results = backtest.test_key_parameters()

if __name__ == "__main__":
    main()
