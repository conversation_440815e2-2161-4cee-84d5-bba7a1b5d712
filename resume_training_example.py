#!/usr/bin/env python3
"""
断点续训功能示例
展示如何使用patent_train_model的断点续训功能
"""

import os
from patent_stock_predictor import (
    patent_train_model, 
    patent_predict, 
    generate_sample_data, 
    get_recommended_early_stopping_params,
    check_model_training_status
)

def demonstrate_resume_training():
    """演示断点续训功能"""
    print("🎯 断点续训功能演示")
    print("=" * 50)
    
    # 准备数据
    print("📊 准备训练数据...")
    num_stocks = 3
    days_per_stock = 50
    sample_data = generate_sample_data(num_stocks=num_stocks, days_per_stock=days_per_stock)
    print(f"生成了{len(sample_data)}只股票的数据，每只股票{len(sample_data[0])}天")
    
    # 获取推荐参数
    params = get_recommended_early_stopping_params(num_stocks, days_per_stock)
    print(f"推荐训练参数: {params}")
    
    model_path = 'resume_demo_model.pth'
    
    # 第一次训练（模拟中断）
    print(f"\n🚀 第一次训练（模拟训练中断）...")
    try:
        # 删除旧模型文件
        if os.path.exists(model_path):
            os.remove(model_path)
            print(f"🗑️ 删除旧模型文件: {model_path}")
        
        # 第一次训练，使用较少的轮数模拟中断
        model, scaler = patent_train_model(
            kline_data_list=sample_data,
            k=5,
            epochs_pretrain=10,  # 较少轮数模拟中断
            epochs_finetune=5,
            model_path=model_path,
            early_stopping=True,
            patience=3,
            min_delta=1e-4,
            resume_training=False  # 第一次训练
        )
        
        print("✅ 第一次训练完成")
        
    except Exception as e:
        print(f"❌ 第一次训练失败: {e}")
        return
    
    # 检查模型状态
    print(f"\n🔍 检查模型训练状态...")
    status = check_model_training_status(model_path)
    if status['exists']:
        print(f"✅ 模型文件存在")
        print(f"- 预训练进度: {status['pretrain_completed']}/{status['total_pretrain']} ({status['pretrain_progress']:.1f}%)")
        print(f"- 微调进度: {status['finetune_completed']}/{status['total_finetune']} ({status['finetune_progress']:.1f}%)")
        print(f"- 训练完成: {status['training_completed']}")
    else:
        print(f"❌ {status['message']}")
        return
    
    # 断点续训
    print(f"\n🔄 开始断点续训...")
    try:
        model, scaler = patent_train_model(
            kline_data_list=sample_data,
            k=5,
            epochs_pretrain=params['epochs_pretrain'],  # 更多轮数
            epochs_finetune=params['epochs_finetune'],
            model_path=model_path,
            early_stopping=True,
            patience=params['patience'],
            min_delta=params['min_delta'],
            resume_training=True,  # 启用断点续训
            resume_from_epoch=0    # 从保存的进度继续
        )
        
        print("✅ 断点续训完成")
        
    except Exception as e:
        print(f"❌ 断点续训失败: {e}")
        return
    
    # 再次检查模型状态
    print(f"\n🔍 检查最终模型状态...")
    final_status = check_model_training_status(model_path)
    if final_status['exists']:
        print(f"✅ 最终模型状态")
        print(f"- 预训练进度: {final_status['pretrain_completed']}/{final_status['total_pretrain']} ({final_status['pretrain_progress']:.1f}%)")
        print(f"- 微调进度: {final_status['finetune_completed']}/{final_status['total_finetune']} ({final_status['finetune_progress']:.1f}%)")
        print(f"- 训练完成: {final_status['training_completed']}")
    
    # 测试预测
    print(f"\n🔮 测试预测功能...")
    try:
        test_data = sample_data[0][-5:]
        result = patent_predict(test_data, model_path=model_path)
        print(f"预测结果: {result}")
        print("✅ 预测功能正常")
    except Exception as e:
        print(f"❌ 预测失败: {e}")

def show_usage_examples():
    """显示使用示例"""
    print(f"\n📖 断点续训使用示例")
    print("=" * 30)
    
    examples = [
        {
            "title": "1. 从头开始训练",
            "code": """
model, scaler = patent_train_model(
    kline_data_list=your_data,
    epochs_pretrain=50,
    epochs_finetune=20,
    model_path='my_model.pth',
    resume_training=False  # 从头开始
)"""
        },
        {
            "title": "2. 断点续训（自动从保存的进度继续）",
            "code": """
model, scaler = patent_train_model(
    kline_data_list=your_data,
    epochs_pretrain=100,  # 可以设置更多轮数
    epochs_finetune=40,
    model_path='my_model.pth',
    resume_training=True   # 启用断点续训
)"""
        },
        {
            "title": "3. 从指定轮数开始训练",
            "code": """
model, scaler = patent_train_model(
    kline_data_list=your_data,
    epochs_pretrain=100,
    epochs_finetune=40,
    model_path='my_model.pth',
    resume_training=True,
    resume_from_epoch=20   # 从第20轮开始
)"""
        },
        {
            "title": "4. 检查模型训练状态",
            "code": """
status = check_model_training_status('my_model.pth')
print(f"预训练进度: {status['pretrain_progress']:.1f}%")
print(f"微调进度: {status['finetune_progress']:.1f}%")
"""
        }
    ]
    
    for example in examples:
        print(f"\n{example['title']}:")
        print(example['code'])

if __name__ == "__main__":
    # 运行演示
    demonstrate_resume_training()
    
    # 显示使用示例
    show_usage_examples()
    
    print(f"\n🎉 断点续训功能演示完成！")
    print(f"💡 提示：可以随时中断训练，然后使用resume_training=True继续训练")
