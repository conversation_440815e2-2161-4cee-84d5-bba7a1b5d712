# K线数据转换工具

这个工具可以将1分钟K线数据合成为10分钟K线数据。

## 功能特点

- ✅ 读取1分钟K线JSON文件
- ✅ 按照标准时间窗口合成10分钟K线
- ✅ 正确处理开盘时间和收盘时间边界
- ✅ 计算准确的开盘价、收盘价、最高价、最低价
- ✅ 保存结果到本地JSON文件
- ✅ 详细的处理日志和错误处理

## K线数据格式

输入和输出的K线数据都采用以下格式：

```json
[
  [
    1591256400000,      // 开盘时间（毫秒时间戳）
    "9653.69440000",    // 开盘价
    "9653.69640000",    // 最高价
    "9651.38600000",    // 最低价
    "9651.55200000",    // 收盘价
    "0",                // 忽略
    1591256459999,      // 收盘时间（毫秒时间戳）
    "0",                // 忽略
    60,                 // 忽略
    "0",                // 忽略
    "0",                // 忽略
    "0"                 // 忽略
  ]
]
```

## 使用方法

### 方法1：直接运行转换工具

```bash
python kline_converter.py
```

默认会读取 `BTCUSDT_1m.json` 文件，并输出到 `BTCUSDT_10m.json`。

### 方法2：在代码中调用函数

```python
from kline_converter import convert_1m_to_10m_klines

# 转换K线数据
result = convert_1m_to_10m_klines(
    input_filename="BTCUSDT_1m.json",
    output_filename="BTCUSDT_10m.json"
)

print(f"生成了 {len(result)} 条10分钟K线数据")
```

## 时间窗口规则

10分钟K线的时间窗口按照以下规则划分：

- 00:00-00:09
- 00:10-00:19
- 00:20-00:29
- 00:30-00:39
- 00:40-00:49
- 00:50-00:59

例如：
- 13:07的1分钟K线会被归入13:00-13:09的10分钟窗口
- 13:15的1分钟K线会被归入13:10-13:19的10分钟窗口

## 合成规则

对于每个10分钟窗口：

- **开盘价**：窗口内第一根1分钟K线的开盘价
- **收盘价**：窗口内最后一根1分钟K线的收盘价
- **最高价**：窗口内所有1分钟K线的最高价中的最大值
- **最低价**：窗口内所有1分钟K线的最低价中的最小值
- **开盘时间**：窗口开始时间
- **收盘时间**：窗口结束时间（开始时间 + 10分钟 - 1毫秒）

## 文件说明

- `kline_converter.py` - 主要的转换工具
- `test.py` - 包含原始的转换函数和数据获取功能
- `create_test_data.py` - 创建测试数据的工具
- `BTCUSDT_1m.json` - 1分钟K线数据文件（输入）
- `BTCUSDT_10m.json` - 10分钟K线数据文件（输出）

## 示例输出

```
==================================================
K线数据转换工具 - 1分钟转10分钟
==================================================
读取到 30 条1分钟K线数据
合成10分钟K线: 2024-01-01 10:00:00 - 2024-01-01 10:09:59
  包含10根1分钟K线
  开盘价: 49950.00, 收盘价: 50035.00
  最高价: 50044.00000000, 最低价: 49945.00000000

✅ 10分钟K线数据已保存到 BTCUSDT_10m.json
📊 共生成 3 条10分钟K线记录
⏰ 时间范围: 2024-01-01 10:00:00 - 2024-01-01 10:20:00

🎉 转换完成！
```

## 注意事项

1. 确保输入的1分钟K线数据文件存在且格式正确
2. 输入数据应该按时间顺序排列
3. 如果某个10分钟窗口内没有1分钟数据，该窗口不会生成10分钟K线
4. 工具会自动处理时间边界，确保10分钟K线的时间对齐到标准窗口
