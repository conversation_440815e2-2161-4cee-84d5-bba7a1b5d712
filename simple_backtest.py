#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版回测系统 - 固定赔率0.8和交易金额5，测试几个关键参数

修复的未来数据泄露问题：
1. 所有技术指标都使用shift(1)，确保在时刻i只使用到i-1的历史数据
2. 当前K线只使用开盘价作为特征（决策时刻唯一可知的信息）
3. 添加前一根K线的完整OHLC数据作为特征
4. 确保预测时不会使用当前K线的高低收盘价等未来信息
5. 修复训练数据时间对齐问题：用时刻i-1的特征预测时刻i的标签
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime
from sklearn.ensemble import RandomForestClassifier
from typing import List, Tuple, Dict
import warnings
warnings.filterwarnings('ignore')

class SimpleBacktestSystem:
    def __init__(self, kline_file: str = "BTCUSDT_10m.json"):
        """初始化简化版回测系统"""
        self.kline_file = kline_file
        self.klines_df = None
        self.win_ratio = 0.8  # 固定赔率
        self.trade_amount = 5  # 固定交易金额
        
    def load_klines(self) -> bool:
        """加载K线数据"""
        try:
            with open(self.kline_file, 'r') as f:
                klines_data = json.load(f)
            
            # 转换为DataFrame
            columns = ['open_time', 'open', 'high', 'low', 'close', 'volume', 
                      'close_time', 'quote_volume', 'count', 'taker_buy_volume', 
                      'taker_buy_quote_volume', 'ignore']
            
            self.klines_df = pd.DataFrame(klines_data, columns=columns)
            
            # 转换数据类型
            self.klines_df['open_time'] = pd.to_datetime(self.klines_df['open_time'], unit='ms')
            self.klines_df['close_time'] = pd.to_datetime(self.klines_df['close_time'], unit='ms')
            self.klines_df['open'] = self.klines_df['open'].astype(float)
            self.klines_df['high'] = self.klines_df['high'].astype(float)
            self.klines_df['low'] = self.klines_df['low'].astype(float)
            self.klines_df['close'] = self.klines_df['close'].astype(float)
            
            # 计算技术指标
            self.calculate_indicators()
            
            print(f"成功加载 {len(self.klines_df)} 条K线数据")
            print(f"数据时间范围: {self.klines_df['open_time'].iloc[0]} 到 {self.klines_df['open_time'].iloc[-1]}")
            return True
            
        except Exception as e:
            print(f"加载K线数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标 - 修复未来数据泄露问题"""
        # 将所有技术指标向前移动一位，确保在时刻i只使用到i-1的历史数据

        # 简单移动平均线（使用前一根K线的收盘价）
        self.klines_df['sma_5'] = self.klines_df['close'].rolling(window=5).mean().shift(1)
        self.klines_df['sma_10'] = self.klines_df['close'].rolling(window=10).mean().shift(1)
        self.klines_df['sma_20'] = self.klines_df['close'].rolling(window=20).mean().shift(1)

        # 指数移动平均线（使用前一根K线的收盘价）
        self.klines_df['ema_5'] = self.klines_df['close'].ewm(span=5).mean().shift(1)
        self.klines_df['ema_10'] = self.klines_df['close'].ewm(span=10).mean().shift(1)

        # RSI指标（使用前一根K线的收盘价）
        self.klines_df['rsi'] = self.calculate_rsi(self.klines_df['close'], 14).shift(1)

        # 价格变化率（使用前一根K线的数据）
        self.klines_df['price_change'] = self.klines_df['close'].pct_change().shift(1)
        self.klines_df['price_change_5'] = self.klines_df['close'].pct_change(5).shift(1)

        # 波动率（使用前一根K线的数据）
        price_change_temp = self.klines_df['close'].pct_change()
        self.klines_df['volatility'] = price_change_temp.rolling(window=10).std().shift(1)

        # 添加当前K线的开盘价作为特征（这是在决策时刻唯一可知的当前K线信息）
        self.klines_df['current_open'] = self.klines_df['open']

        # 添加前一根K线的OHLC作为特征
        self.klines_df['prev_open'] = self.klines_df['open'].shift(1)
        self.klines_df['prev_high'] = self.klines_df['high'].shift(1)
        self.klines_df['prev_low'] = self.klines_df['low'].shift(1)
        self.klines_df['prev_close'] = self.klines_df['close'].shift(1)

        # 下一个开盘价（用于判断输赢）
        self.klines_df['next_open'] = self.klines_df['open'].shift(-1)

        # 涨跌标签（当前开盘价 vs 下一个开盘价）
        self.klines_df['next_up'] = (self.klines_df['next_open'] > self.klines_df['open']).astype(int)
    
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def prepare_features(self) -> Tuple[np.ndarray, np.ndarray, pd.DataFrame]:
        """准备机器学习特征 - 只使用决策时刻可获得的数据"""
        feature_columns = [
            # 技术指标（基于历史数据，已经shift过）
            'sma_5', 'sma_10', 'sma_20', 'ema_5', 'ema_10',
            'rsi', 'price_change', 'price_change_5', 'volatility',
            # 当前K线开盘价（决策时刻唯一可知的当前信息）
            'current_open',
            # 前一根K线的完整信息
            'prev_open', 'prev_high', 'prev_low', 'prev_close'
        ]

        # 去除包含NaN的行
        df_clean = self.klines_df.dropna()

        # 准备特征和标签
        features = df_clean[feature_columns].values
        labels = df_clean['next_up'].values

        print(f"特征维度: {features.shape}")
        print(f"特征列表: {feature_columns}")

        return features, labels, df_clean

    def validate_no_future_leak(self, sample_size: int = 5) -> bool:
        """验证是否存在未来数据泄露"""
        print("\n" + "="*50)
        print("验证未来数据泄露修复情况")
        print("="*50)

        features, labels, df_clean = self.prepare_features()

        if len(df_clean) < 50:
            print("数据不足，无法验证")
            return False

        # 检查几个样本点
        for i in range(30, min(35, len(df_clean)-1)):
            current_time = df_clean.iloc[i]['open_time']
            current_open = df_clean.iloc[i]['open']
            current_close = df_clean.iloc[i]['close']
            next_open = df_clean.iloc[i+1]['open']

            # 获取当前时刻的特征
            current_features = features[i]
            feature_columns = [
                'sma_5', 'sma_10', 'sma_20', 'ema_5', 'ema_10',
                'rsi', 'price_change', 'price_change_5', 'volatility',
                'current_open', 'prev_open', 'prev_high', 'prev_low', 'prev_close'
            ]

            print(f"\n时刻 {i}: {current_time}")
            print(f"当前K线 - 开盘: {current_open:.2f}, 收盘: {current_close:.2f}")
            print(f"下一K线开盘: {next_open:.2f}")

            # 检查current_open特征是否正确
            current_open_feature = current_features[9]  # current_open在特征列表中的位置
            if abs(current_open_feature - current_open) > 0.01:
                print(f"❌ 错误：current_open特征 {current_open_feature} 与实际开盘价 {current_open} 不匹配")
                return False
            else:
                print(f"✅ current_open特征正确: {current_open_feature}")

            # 检查技术指标是否使用了当前K线的收盘价（应该没有）
            prev_close_feature = current_features[13]  # prev_close在特征列表中的位置
            if i > 0:
                actual_prev_close = df_clean.iloc[i-1]['close']
                if abs(prev_close_feature - actual_prev_close) > 0.01:
                    print(f"❌ 错误：prev_close特征 {prev_close_feature} 与实际前一收盘价 {actual_prev_close} 不匹配")
                    return False
                else:
                    print(f"✅ prev_close特征正确: {prev_close_feature}")

        print(f"\n✅ 验证通过：未来数据泄露问题已修复")
        print("- 所有技术指标都基于历史数据")
        print("- 当前K线只使用开盘价")
        print("- 不会使用当前K线的高低收盘价")
        return True
    
    def run_backtest(self, train_period: int = 100, sample_rate: int = 20,
                    n_estimators: int = 20, initial_balance: float = 1000, 
                    verbose: bool = True) -> Dict:
        """运行回测"""
        
        features, labels, df_clean = self.prepare_features()
        
        if len(features) < train_period + 50:
            if verbose:
                print(f"数据不足，需要至少 {train_period + 50} 条有效数据")
            return None
        
        # 初始化变量
        current_balance = initial_balance
        trades = []
        predictions = []
        
        sample_points = list(range(train_period, len(features) - 1, sample_rate))
        
        if verbose:
            print(f"开始回测，训练周期: {train_period}, 采样率: {sample_rate}")
            print(f"树的数量: {n_estimators}, 实际预测次数: {len(sample_points)}")
        
        for idx, i in enumerate(sample_points):
            # 显示进度
            if verbose and idx % 200 == 0:
                progress = idx / len(sample_points) * 100
                print(f"进度: {progress:.1f}% ({idx}/{len(sample_points)})")

            # 准备训练数据 - 修复时间对齐问题
            # 使用 i-train_period 到 i-1 的特征预测 i-train_period+1 到 i 的标签
            X_train = features[i-train_period:i-1]
            y_train = labels[i-train_period+1:i]

            # 预测数据 - 使用当前时刻i的特征（已经修正为只包含历史数据）
            X_predict = features[i:i+1]

            # 训练模型（不使用并行处理避免问题）
            clf = RandomForestClassifier(n_estimators=n_estimators, random_state=42, n_jobs=1)
            clf.fit(X_train, y_train)

            # 预测
            prediction = clf.predict(X_predict)[0]

            # 获取价格信息
            current_open = df_clean.iloc[i]['open']
            next_open = df_clean.iloc[i+1]['open']
            current_time = df_clean.iloc[i]['open_time']
            actual_up = 1 if next_open > current_open else 0

            # 验证数据完整性
            if verbose and idx < 5:  # 只在前几次显示详细信息
                print(f"\n时刻 {idx}: {current_time}")
                print(f"当前开盘价: {current_open}, 下一开盘价: {next_open}")
                print(f"预测: {'上涨' if prediction == 1 else '下跌'}, 实际: {'上涨' if actual_up == 1 else '下跌'}")
            
            # 记录预测
            predictions.append({
                'time': current_time,
                'prediction': prediction,
                'actual': actual_up,
                'current_open': current_open,
                'next_open': next_open
            })
            
            # 执行交易
            if current_balance >= self.trade_amount:
                win = (prediction == actual_up)
                
                if win:
                    profit = self.trade_amount * self.win_ratio
                    current_balance += profit
                    result = "WIN"
                else:
                    loss = self.trade_amount
                    current_balance -= loss
                    result = "LOSS"
                
                trades.append({
                    'time': current_time,
                    'prediction': "买多" if prediction == 1 else "买空",
                    'trade_amount': self.trade_amount,
                    'result': result,
                    'profit_loss': profit if win else -loss,
                    'balance': current_balance,
                    'current_open': current_open,
                    'next_open': next_open
                })
            else:
                if verbose:
                    print(f"资金不足，停止交易。当前余额: {current_balance:.2f}")
                break
        
        # 计算结果
        if not trades:
            return None
            
        pred_df = pd.DataFrame(predictions[:len(trades)])
        trade_df = pd.DataFrame(trades)
        
        accuracy = (pred_df['prediction'] == pred_df['actual']).mean()
        win_rate = len(trade_df[trade_df['result'] == 'WIN']) / len(trade_df)
        total_return = (current_balance - initial_balance) / initial_balance
        
        # 计算最大回撤
        balance_series = trade_df['balance']
        running_max = balance_series.expanding().max()
        drawdown = (balance_series - running_max) / running_max
        max_drawdown = drawdown.min()
        
        result = {
            'train_period': train_period,
            'sample_rate': sample_rate,
            'n_estimators': n_estimators,
            'accuracy': accuracy,
            'win_rate': win_rate,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'total_trades': len(trades),
            'final_balance': current_balance,
            'trades': trades
        }
        
        if verbose:
            self.print_results(result)
        
        return result
    
    def print_results(self, result: Dict):
        """打印回测结果"""
        print("\n" + "="*50)
        print("回测结果分析")
        print("="*50)
        print(f"训练周期: {result['train_period']}")
        print(f"采样率: {result['sample_rate']}")
        print(f"随机森林树数量: {result['n_estimators']}")
        print(f"预测准确率: {result['accuracy']:.2%}")
        print(f"总交易次数: {result['total_trades']}")
        print(f"交易胜率: {result['win_rate']:.2%}")
        print(f"总收益率: {result['total_return']:.2%}")
        print(f"最大回撤: {result['max_drawdown']:.2%}")
        print(f"最终余额: {result['final_balance']:.2f}")
        
        # 显示最近几次交易
        if result['trades']:
            trade_df = pd.DataFrame(result['trades'])
            print(f"\n最近10次交易:")
            print(trade_df[['time', 'prediction', 'result', 'profit_loss', 'balance']].tail(10).to_string(index=False))
    
    def test_key_parameters(self):
        """测试几个关键参数组合"""
        print("测试关键参数组合...")
        print(f"固定参数: 赔率={self.win_ratio}, 交易金额={self.trade_amount}")
        
        # 测试参数组合
        test_cases = [
            {'train_period': 50, 'sample_rate': 10, 'n_estimators': 10},
            {'train_period': 100, 'sample_rate': 20, 'n_estimators': 20},
            {'train_period': 150, 'sample_rate': 30, 'n_estimators': 30},
            {'train_period': 200, 'sample_rate': 50, 'n_estimators': 20},
        ]
        
        results = []
        
        for i, params in enumerate(test_cases):
            print(f"\n测试案例 {i+1}/{len(test_cases)}")
            print(f"参数: {params}")
            
            result = self.run_backtest(
                train_period=params['train_period'],
                sample_rate=params['sample_rate'],
                n_estimators=params['n_estimators'],
                initial_balance=1000,
                verbose=True
            )
            
            if result:
                results.append(result)
        
        # 比较结果
        if results:
            print("\n" + "="*80)
            print("参数测试结果比较")
            print("="*80)
            
            results_df = pd.DataFrame(results)
            results_df_sorted = results_df.sort_values('total_return', ascending=False)
            
            print("按总收益率排序:")
            print(results_df_sorted[['train_period', 'sample_rate', 'n_estimators',
                                    'accuracy', 'win_rate', 'total_return', 'max_drawdown', 
                                    'total_trades']].to_string(index=False))
            
            # 找出最佳参数
            best_result = results_df_sorted.iloc[0]
            print(f"\n最佳参数组合:")
            print(f"训练周期: {best_result['train_period']}")
            print(f"采样率: {best_result['sample_rate']}")
            print(f"树数量: {best_result['n_estimators']}")
            print(f"预测准确率: {best_result['accuracy']:.2%}")
            print(f"交易胜率: {best_result['win_rate']:.2%}")
            print(f"总收益率: {best_result['total_return']:.2%}")
            print(f"最大回撤: {best_result['max_drawdown']:.2%}")
            
            return results_df_sorted
        else:
            print("没有有效的回测结果")
            return None

def main():
    """主函数"""
    # 创建简化版回测系统
    backtest = SimpleBacktestSystem(kline_file="BTCUSDT_10m.json")

    # 加载数据
    if not backtest.load_klines():
        return

    # 验证未来数据泄露修复情况
    if not backtest.validate_no_future_leak():
        print("❌ 数据验证失败，请检查代码")
        return

    # 测试关键参数
    print("\n开始参数测试...")
    backtest.test_key_parameters()

if __name__ == "__main__":
    main()